@Article{fi13020054,
AUTHOR = {<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON>},
TITLE = {Load Balancing Oriented Predictive Routing Algorithm for Data Center Networks},
JOURNAL = {Future Internet},
VOLUME = {13},
YEAR = {2021},
NUMBER = {2},
ARTICLE-NUMBER = {54},
URL = {https://www.mdpi.com/1999-5903/13/2/54},
ISSN = {1999-5903},
DOI = {10.3390/fi13020054}
}

@article{hmmlb,
author = {<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, Chang},
year = {2019},
month = {12},
pages = {},
title = {Hidden Markov Model-based Load Balancing in Data Center Networks},
volume = {63},
journal = {The Computer Journal},
doi = {10.1093/comjnl/bxz142}
}

@inproceedings{chang2023learned,
  title={Learned load balancing},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON>},
  booktitle={Proceedings of the 24th International Conference on Distributed Computing and Networking},
  pages={177--187},
  year={2023}
}

@inproceedings{ruelas2018load,
  title={A load balancing method based on artificial neural networks for knowledge-defined data center networking},
  author={Ruelas, Alex MR and Rothenberg, Christian Esteve},
  booktitle={Proceedings of the 10th Latin America Networking Conference},
  pages={106--109},
  year={2018}
}

@inproceedings{liu2022bulb,
  title={BULB: lightweight and automated load balancing for fast datacenter networks},
  author={Liu, Yuan and Li, Wenxin and Qu, Wenyu and Qi, Heng},
  booktitle={Proceedings of the 51st International Conference on Parallel Processing},
  pages={1--11},
  year={2022}
}

@article{babayigit2021deep,
  title={Deep learning for load balancing of SDN-based data center networks},
  author={Babayigit, Bilal and Ulu, Banu},
  journal={International Journal of Communication Systems},
  volume={34},
  number={7},
  pages={e4760},
  year={2021},
  publisher={Wiley Online Library}
}

@article{rikhtegar2021deeprlb,
  title={DeepRLB: A deep reinforcement learning-based load balancing in data center networks},
  author={Rikhtegar, Negar and Bushehrian, Omid and Keshtgari, Manijeh},
  journal={International Journal of Communication Systems},
  volume={34},
  number={15},
  pages={e4912},
  year={2021},
  publisher={Wiley Online Library}
}

@inproceedings{tosounidis2020deep,
  title={Deep Q-learning for load balancing traffic in SDN networks},
  author={Tosounidis, Vasileios and Pavlidis, Georgios and Sakellariou, Ilias},
  booktitle={11th Hellenic Conference on Artificial Intelligence},
  pages={135--143},
  year={2020}
}

@inproceedings{xiangyun2021deep,
  title={Deep reinforcement learning with graph convolutional networks for load balancing in SDN-based data center networks},
  author={Xiangyun, Zeng and Lijun, Wu and Zhiyuan, Li and Yulin, Jing},
  booktitle={2021 18th International Computer Conference on Wavelet Active Media Technology and Information Processing (ICCWAMTIP)},
  pages={344--352},
  year={2021},
  organization={IEEE}
}

@inproceedings{hu2023deep,
  title={Deep Reinforcement Learning Based Load Balancing for Heterogeneous Traffic in Datacenter Networks},
  author={Hu, Jinbin and Luo, Wangqing and He, Yi and Wang, Jing and Zhang, Dengyong},
  booktitle={International Conference on Algorithms and Architectures for Parallel Processing},
  pages={270--289},
  year={2023},
  organization={Springer}
}

@article{fancy2021intelligence,
  title={Intelligence-enabled approach for load balancing in software-defined data center networks},
  author={Fancy, C and Pushpalatha, M},
  journal={International Journal of Communication Systems},
  volume={34},
  number={9},
  year={2021},
  publisher={WILEY 111 RIVER ST, HOBOKEN 07030-5774, NJ USA}
}

@inproceedings{hao2023link,
  title={Link load balancing scheme for elephant flow in SDN data center},
  author={Hao, Feifan and Jing, Shan and Zhao, Chuan},
  booktitle={2023 IEEE Intl Conf on Parallel \& Distributed Processing with Applications, Big Data \& Cloud Computing, Sustainable Computing \& Communications, Social Computing \& Networking (ISPA/BDCloud/SocialCom/SustainCom)},
  pages={1026--1033},
  year={2023},
  organization={IEEE}
}

@inproceedings{lin2019rilnet,
  title={Rilnet: A reinforcement learning based load balancing approach for datacenter networks},
  author={Lin, Qinliang and Gong, Zhibo and Wang, Qiaoling and Li, Jinlong},
  booktitle={Machine Learning for Networking: First International Conference, MLN 2018, Paris, France, November 27--29, 2018, Revised Selected Papers 1},
  pages={44--55},
  year={2019},
  organization={Springer}
}

@inproceedings{lim2021reinforcement,
  title={Reinforcement learning based load balancing for data center networks},
  author={Lim, Jiyoon and Yoo, Jae-Hyoung and Hong, James Won-Ki},
  booktitle={2021 IEEE 7th International Conference on Network Softwarization (NetSoft)},
  pages={151--155},
  year={2021},
  organization={IEEE}
}

@article{liu2021drl,
  title={DRL-PLink: Deep reinforcement learning with private link approach for mix-flow scheduling in software-defined data-center networks},
  author={Liu, Wai-Xi and Lu, Jinjie and Cai, Jun and Zhu, Yinghao and Ling, Sen and Chen, Qingchun},
  journal={IEEE Transactions on Network and Service Management},
  volume={19},
  number={2},
  pages={1049--1064},
  year={2021},
  publisher={IEEE}
}

@article{guo2020aggreflow,
  title={AggreFlow: Achieving power efficiency, load balancing, and quality of service in data center networks},
  author={Guo, Zehua and Xu, Yang and Liu, Ya-Feng and Liu, Sen and Chao, H Jonathan and Zhang, Zhi-Li and Xia, Yuanqing},
  journal={IEEE/ACM Transactions on Networking},
  volume={29},
  number={1},
  pages={17--33},
  year={2020},
  publisher={IEEE}
}

@article{rikhtegar2021bite,
  title={BiTE: a dynamic bi-level traffic engineering model for load balancing and energy efficiency in data center networks},
  author={Rikhtegar, Negar and Keshtgari, Manijeh and Bushehrian, Omid and Pujolle, Guy},
  journal={Applied Intelligence},
  volume={51},
  pages={4623--4648},
  year={2021},
  publisher={Springer}
}

@article{he2021joint,
  title={Joint optimization of energy saving and load balancing for data center networks based on software defined networks},
  author={He, Yihao and Lu, Zebin and Lei, Junru and Deng, Shuhua and Gao, Xieping},
  journal={Concurrency and Computation: Practice and Experience},
  volume={33},
  number={9},
  pages={e6134},
  year={2021},
  publisher={Wiley Online Library}
}

@article{pathan2024priority,
  title={Priority based energy and load aware routing algorithms for SDN enabled data center network},
  author={Pathan, Md Naimul and Muntaha, Maisha and Sharmin, Selina and Saha, Sajeeb and Uddin, Md Ashraf and Nur, Fernaz Narin and Aryal, Sunil},
  journal={Computer Networks},
  volume={240},
  pages={110166},
  year={2024},
  publisher={Elsevier}
}

@article{irteza2018efficient,
  title={Efficient load balancing over asymmetric datacenter topologies},
  author={Irteza, Syed Mohammad and Bashir, Hafiz Mohsin and Anwar, Talal and Qazi, Ihsan Ayyub and Dogar, Fahad Rafique},
  journal={Computer Communications},
  volume={127},
  pages={1--12},
  year={2018},
  publisher={Elsevier}
}

@inproceedings{shu2023dynamic,
  title={A Dynamic Multipath Load Balancing Algorithm Based on Particle Swarm Optimization in DCN},
  author={Shu, Yongan and Gao, Dongsong},
  booktitle={2023 IEEE 14th International Conference on Software Engineering and Service Science (ICSESS)},
  pages={126--130},
  year={2023},
  organization={IEEE}
}

@inproceedings{guohao2020data,
  title={A data center load balancing algorithm based on artificial bee colony algorithm},
  author={Guohao, Yao and Muqing, Wu and Xu, Yang},
  booktitle={2020 IEEE 6th international conference on computer and communications (ICCC)},
  pages={1770--1775},
  year={2020},
  organization={IEEE}
}

@inproceedings{zheng2019dynamic,
  title={Dynamic load balancing in hybrid switching data center networks with converters},
  author={Zheng, Jiaqi and Zheng, Qiming and Gao, Xiaofeng and Chen, Guihai},
  booktitle={Proceedings of the 48th International Conference on Parallel Processing},
  pages={1--10},
  year={2019}
}

@article{chung2021dynamic,
  title={Dynamic parallel flow algorithms with centralized scheduling for load balancing in cloud data center networks},
  author={Chung, Wei-Kang and Li, Yun and Ke, Chih-Heng and Hsieh, Sun-Yuan and Zomaya, Albert Y and Buyya, Rajkumar},
  journal={IEEE Transactions on Cloud Computing},
  volume={11},
  number={1},
  pages={1050--1064},
  year={2021},
  publisher={IEEE}
}

@article{alawadi2022oddlab,
  title={Oddlab: fault-tolerant aware load-balancing framework for data center networks},
  author={Alawadi, Aymen Hasan and Moln{\'a}r, S{\'a}ndor},
  journal={Annals of Telecommunications},
  pages={1--22},
  year={2022},
  publisher={Springer}
}

@article{yan2023st,
  title={ST-Resnet: a deep learning-based privacy preserving differential publishing method for location statistics},
  author={Yan, Yan and Sun, Zichao and Mahmood, Adnan and Cong, Yiming and Xu, Fei and Sheng, Quan Z},
  journal={Computing},
  volume={105},
  number={11},
  pages={2363--2387},
  year={2023},
  publisher={Springer}
}



@INPROCEEDINGS{MATCP,
  author={Xia, Yu and Wu, Jinsong and Xia, Jingwen and Wang, Ting and Mao, Sun},
  booktitle={2021 IEEE/ACM 29th International Symposium on Quality of Service (IWQOS)}, 
  title={Multipath-aware TCP for Data Center Traffic Load-balancing}, 
  year={2021},
  volume={},
  number={},
  pages={1-6},
  keywords={Performance evaluation;Data centers;Waste materials;Quality of service;Load management;Data models;Topology;data center;load-balancing;multipath;TCP},
  doi={10.1109/IWQOS52092.2021.9521276}}

@ARTICLE{QLLB,
	author = {Ahmed, Hasnain and Arshad, Muhammad Junaid and Muhammad, Shah and Ahmad, Sarfraz and Zahid, Amjad Hussain},
	title = {Queue length-based load balancing in data center networks},
	year = {2020},
	journal = {International Journal of Communication Systems},
	volume = {33},
	number = {14},
	doi = {10.1002/dac.4472},
	url = {https://www.scopus.com/inward/record.uri?eid=2-s2.0-85087307757&doi=10.1002%2fdac.4472&partnerID=40&md5=744cbca1d26f9a50fb00f1e56460410b},
	type = {Article},
	publication_stage = {Final},
	source = {Scopus},
	note = {Cited by: 4}
}

@article{BLEND,
title = {The endonuclease domain of Bacillus subtilis MutL is functionally asymmetric},
journal = {DNA Repair},
volume = {73},
pages = {1-6},
year = {2019},
issn = {1568-7864},
doi = {https://doi.org/10.1016/j.dnarep.2018.10.003},
url = {https://www.sciencedirect.com/science/article/pii/S1568786418301460},
author = {Linda Liu and Mary Carmen {Ortiz Castro} and Javier {Rodríguez González} and Monica C. Pillon and Alba Guarné},
keywords = {DNA mismatch repair, Sliding β-clamp, MutL, MutLα, Structure specific nucleases}}

@article{OPLB,
  title={Online elephant flow prediction for load balancing in programmable switch-based DCN},
  author={Xie, Shengxu and Hu, Guyu and Xing, Changyou and Liu, Yaqun},
  journal={IEEE Transactions on Network and Service Management},
  volume={21},
  number={1},
  pages={745--758},
  year={2023},
  publisher={IEEE}
}

@INPROCEEDINGS{EMAN,
  author={Zhang, Weifeng and Ling, Dongfang and Zhang, Yuanrong and Li, Pengfei and Chen, Guo},
  booktitle={2020 IFIP Networking Conference (Networking)}, 
  title={Achieving Optimal Edge-based Congestion-aware Load Balancing in Data Center Networks}, 
  year={2020},
  volume={},
  number={},
  pages={109-117},
  keywords={Bandwidth;Load management;Throughput;Switches;Image edge detection;Monitoring;Data centers},
  doi={}}

@INPROCEEDINGS{CAFT,
  author={Alanazi, Sultan and Hamdaoui, Bechir},
  booktitle={2020 International Wireless Communications and Mobile Computing (IWCMC)}, 
  title={CAFT: Congestion-Aware Fault-Tolerant Load Balancing for Three-Tier Clos Data Centers}, 
  year={2020},
  volume={},
  number={},
  pages={1746-1751},
  keywords={Load management;Data centers;Resource management;Topology;Protocols;Measurement;Network topology;Load balancing;data center networks;network congestion;distributed routing},
  doi={10.1109/IWCMC48107.2020.9148271}}

@article{DFFR,
title = {DFFR: A flow-based approach for distributed load balancing in Data Center Networks},
journal = {Computer Communications},
volume = {116},
pages = {1-8},
year = {2018},
issn = {0140-3664},
doi = {https://doi.org/10.1016/j.comcom.2017.11.001},
url = {https://www.sciencedirect.com/science/article/pii/S0140366417302906},
author = {Chung-Ming Cheung and Ka-Cheong Leung},
keywords = {Data Center Networks, Load balancing}}

@ARTICLE{DLB,
	author = {Zhang, Yaochuan and Wang, Xiaoliang and Shi, Mengwu and Song, Yifu and Yu, Juanhan and Han, Sheng},
	title = {Programmed death ligand 1 and tumor-infiltrating CD8+ T lymphocytes are associated with the clinical features in meningioma},
	year = {2022},
	journal = {BMC Cancer},
	volume = {22},
	number = {1},
	doi = {10.1186/s12885-022-10249-4},
	url = {https://www.scopus.com/inward/record.uri?eid=2-s2.0-***********&doi=10.1186%2fs12885-022-10249-4&partnerID=40&md5=463bd915e6e5d3954372cef8e84b2d84},
	type = {Article},
	publication_stage = {Final},
	source = {Scopus},
	note = {Cited by: 9; All Open Access, Gold Open Access, Green Open Access}
}

@ARTICLE{DDMP,
	author = {Wang, Yuqi and Wang, Liangxu and Sun, Yanli and Wu, Miao and Ma, Yingjie and Yang, Lingping and Meng, Chun and Zhong, Li and Hossain, Mohammad Arman and Peng, Bin},
	title = {Prediction model for the risk of osteoporosis incorporating factors of disease history and living habits in physical examination of population in Chongqing, Southwest China: based on artificial neural network},
	year = {2021},
	journal = {BMC Public Health},
	volume = {21},
	number = {1},
	doi = {10.1186/s12889-021-11002-5},
	url = {https://www.scopus.com/inward/record.uri?eid=2-s2.0-85106925411&doi=10.1186%2fs12889-021-11002-5&partnerID=40&md5=05a65ccc01f6953b0a273db6801bbde8},
	type = {Article},
	publication_stage = {Final},
	source = {Scopus},
	note = {Cited by: 19; All Open Access, Gold Open Access, Green Open Access}
}

@article{MDTLB,
author = {Memon, Sheeba and Huang, Jiawei and Mirbahar, Saajid and Mirbahar, Naadiya and Saleem, Arshad and Aljeroudi, Yazan},
year = {2019},
month = {01},
pages = {},
title = {Novel Multi-Level Dynamic Traffic Load-Balancing Protocol for Data Center},
volume = {11},
journal = {Symmetry},
doi = {10.3390/sym11020145}
}

@article{HPLB,
author = {Gao, Weimin and Huang, Jiawei and Jiang, Ning and Li, Zhaoyi and Zou, Shaojun and He, Zhidong and Wang, Jianxin},
year = {2022},
month = {09},
pages = {},
title = {HPLB: High precision load balancing based on in-band network telemetry in data center networks},
volume = {15},
journal = {Peer-to-Peer Networking and Applications},
doi = {10.1007/s12083-022-01381-w}
}

@article{CAF,
title = {Achieving high utilization of flowlet-based load balancing in data center networks},
journal = {Future Generation Computer Systems},
volume = {108},
pages = {546-559},
year = {2020},
issn = {0167-739X},
doi = {https://doi.org/10.1016/j.future.2020.03.016},
url = {https://www.sciencedirect.com/science/article/pii/S0167739X19319272},
author = {Shaojun Zou and Jiawei Huang and Wanchun Jiang and Jianxin Wang},
keywords = {Data center, Load balancing, TCP}
}

@article{Flex,
title = {Flex: A flowlet-level load balancing based on load-adaptive timeout in DCN},
journal = {Future Generation Computer Systems},
volume = {130},
pages = {219-230},
year = {2022},
issn = {0167-739X},
doi = {https://doi.org/10.1016/j.future.2021.12.021},
url = {https://www.sciencedirect.com/science/article/pii/S0167739X21005021},
author = {Xinglong Diao and Huaxi Gu and Xiaoshan Yu and Liang Qin and Changyun Luo},
keywords = {Data center networks, Load balancing, Flowlet, Fine-grained, Adaptive}
}

@INPROCEEDINGS{MLAB,
  author={Fan, Fujie and Hu, Bing and Yeung, Kwan L.},
  booktitle={IEEE INFOCOM 2019 - IEEE Conference on Computer Communications}, 
  title={Routing in Black Box: Modularized Load Balancing for Multipath Data Center Networks}, 
  year={2019},
  volume={},
  number={},
  pages={1639-1647},
  keywords={Routing;Load management;Data centers;Switches;Bandwidth;Servers;Heuristic algorithms},
  doi={10.1109/INFOCOM.2019.8737650}}

@InProceedings{Luopan,
author="Wang, Dongshu
and He, Jialing
and Rahim, Mussadiq Abdul
and Zhang, Zijian
and Zhu, Liehuang",
editor="Zhu, Liehuang
and Zhong, Sheng",
title="An Efficient Sparse Coding-Based Data-Mining Scheme in Smart Grid",
booktitle="Mobile Ad-hoc and Sensor Networks",
year="2018",
publisher="Springer Singapore",
address="Singapore",
pages="133--145"
}

@ARTICLE{RSLB,
  author={Liu, Yong and Gu, Huaxi and Zhou, Zhaoxing and Wang, Ning},
  journal={IEEE Transactions on Network and Service Management}, 
  title={RSLB: Robust and Scalable Load Balancing in Software-Defined Data Center Networks}, 
  year={2022},
  volume={19},
  number={4},
  pages={4706-4720},
  keywords={Topology;Load management;Routing;Network topology;Scalability;Data centers;Robustness;SDN;data center networks;load balancing;congestion-aware;distributed control},
  doi={10.1109/TNSM.2022.3192133}}

@INPROCEEDINGS{QDAPS,
  author={Huang, Jiawei and Lv, Wenjun and Li, Weihe and Wang, Jianxin and He, Tian},
  booktitle={2018 IEEE 26th International Conference on Network Protocols (ICNP)}, 
  title={QDAPS: Queueing Delay Aware Packet Spraying for Load Balancing in Data Center}, 
  year={2018},
  volume={},
  number={},
  pages={66-76},
  keywords={Switches;Delays;Load management;Out of order;Data centers;Topology;Receivers;Data center, Multi-path, Load balancing},
  doi={10.1109/ICNP.2018.00017}}

@ARTICLE{RMC,
  author={Zou, Shaojun and Huang, Jiawei and Wang, Jianxin and He, Tian},
  journal={IEEE Transactions on Communications}, 
  title={RMC: Reordering Marking and Coding for Fine-Grained Load Balancing in Data Centers}, 
  year={2021},
  volume={69},
  number={12},
  pages={8363-8374},
  keywords={Delays;Switches;Out of order;Encoding;Data centers;Uncertainty;Topology;Data center;packet reordering;marking;network asymmetry},
  doi={10.1109/TCOMM.2021.3118467}}

@article{QALL,
  title={QALL: distributed queue-behavior-aware load balancing using programmable data planes},
  author={Liu, Wai-Xi and Cai, Jun and Ling, Sen and Zhang, Jian-Yu and Chen, Qingchun},
  journal={IEEE Transactions on Network and Service Management},
  volume={21},
  number={2},
  pages={2303--2322},
  year={2023},
  publisher={IEEE}
}

@ARTICLE{APS,
	author = {Liu, Xiayi and Yu, Ting and Wan, Wenhai},
	title = {Stick to Convention or Bring Forth the New? Research on the Relationship Between Employee Conscientiousness and Job Crafting},
	year = {2020},
	journal = {Frontiers in Psychology},
	volume = {11},
	doi = {10.3389/fpsyg.2020.01038},
	url = {https://www.scopus.com/inward/record.uri?eid=2-s2.0-85086146881&doi=10.3389%2ffpsyg.2020.01038&partnerID=40&md5=58f03d002c52d7a38122cceaddac8c1a},
	type = {Article},
	publication_stage = {Final},
	source = {Scopus},
	note = {Cited by: 4; All Open Access, Gold Open Access, Green Open Access}
}

@article{LBPP,
title = {Load balancing inside programmable data planes based on network modeling prediction using a GNN with network behaviors},
journal = {Computer Networks},
volume = {227},
pages = {109695},
year = {2023},
issn = {1389-1286},
doi = {https://doi.org/10.1016/j.comnet.2023.109695},
url = {https://www.sciencedirect.com/science/article/pii/S1389128623001408},
author = {Wai-Xi Liu and Jun Cai and Ying-Hao Zhu and Jun-Ming Luo and Jin Li},
keywords = {Network modeling, Graph neural networks, Network behavior, Load balancing, Programmable data plane}
}

@article{FAMG,
title = {FAMG: A flow-aware and mixed granularity method for load-balancing in data center networks},
journal = {Computer Communications},
volume = {209},
pages = {415-428},
year = {2023},
issn = {0140-3664},
doi = {https://doi.org/10.1016/j.comcom.2023.07.018},
url = {https://www.sciencedirect.com/science/article/pii/S0140366423002517},
author = {Yifei Lu and Zhengzhi Xu and Xu Ma},
keywords = {Data center networks, Load balancing, Mixed granularity, RPS, Flowlet}
}

@INPROCEEDINGS{FlowDecider,
  author={Qin, Liang and Wei, Wenting and Diao, Xinglong},
  booktitle={2021 IEEE 21st International Conference on Communication Technology (ICCT)}, 
  title={FlowDecider: End-Host Driven Proactive Load Balancing for Data Center Networks}, 
  year={2021},
  volume={},
  number={},
  pages={931-936},
  keywords={Ports (computers);Data centers;Network topology;Simulation;Switches;Telecommunication traffic;Load management;DCN;Load Balancing;timeliness;flowcell},
  doi={10.1109/ICCT52962.2021.9658099}}

@ARTICLE{IntFlow,
  author={Shi, Qingyu and Wang, Fang and Feng, Dan},
  journal={IEEE Transactions on Network and Service Management}, 
  title={IntFlow: Integrating Per-Packet and Per-Flowlet Switching Strategy for Load Balancing in Datacenter Networks}, 
  year={2020},
  volume={17},
  number={3},
  pages={1377-1388},
  keywords={Switches;Load management;Topology;Bandwidth;Throughput;Protocols;Monitoring;Datacenter networks;load balancing;programmable switches},
  doi={10.1109/TNSM.2020.2990868}}

@INPROCEEDINGS{BOBBLE,
  author={Xu, Zhengzhi and Lu, Yifei and Ma, Xu},
  booktitle={2021 IEEE 23rd Int Conf on High Performance Computing & Communications; 7th Int Conf on Data Science & Systems; 19th Int Conf on Smart City; 7th Int Conf on Dependability in Sensor, Cloud & Big Data Systems & Application (HPCC/DSS/SmartCity/DependSys)}, 
  title={BOBBLE: A Mixed Routing-Granularity Distributed Load Balancing for Data Center Networks}, 
  year={2021},
  volume={},
  number={},
  pages={470-477},
  keywords={Data centers;Network topology;Smart cities;Load management;Routing;Mice;Computer networks;Data center networks;load balancing granularity},
  doi={10.1109/HPCC-DSS-SmartCity-DependSys53884.2021.00086}}

@article{ILB,
title = {Load balancing with traffic isolation in data center networks},
journal = {Future Generation Computer Systems},
volume = {127},
pages = {126-141},
year = {2022},
issn = {0167-739X},
doi = {https://doi.org/10.1016/j.future.2021.09.002},
url = {https://www.sciencedirect.com/science/article/pii/S0167739X2100340X},
author = {Tao Zhang and Qianqiang Zhang and Yasi Lei and Shaojun Zou and Juan Huang and Fangmin Li},
keywords = {Leaf–spine, Flow collision, Parallel paths, Load balancing, Data center networks}
}

@article{AG,
title = {Study on dispersion, mechanical and microstructure properties of cement paste incorporating graphene sheets},
journal = {Construction and Building Materials},
volume = {199},
pages = {1-11},
year = {2019},
issn = {0950-0618},
doi = {https://doi.org/10.1016/j.conbuildmat.2018.12.006},
url = {https://www.sciencedirect.com/science/article/pii/S0950061818329866},
author = {Jintao Liu and Jiali Fu and Yang Yang and Chunping Gu},
keywords = {Graphene, Mechanical properties, Dispersion, Microstructure}
}

@ARTICLE{TLB,
  author={Hu, Jinbin and Huang, Jiawei and Lyu, Wenjun and Li, Weihe and Li, Zhaoyi and Jiang, Wenchao and Wang, Jianxin and He, Tian},
  journal={IEEE/ACM Transactions on Networking}, 
  title={Adjusting Switching Granularity of Load Balancing for Heterogeneous Datacenter Traffic}, 
  year={2021},
  volume={29},
  number={5},
  pages={2367-2384},
  keywords={Switches;Load management;Throughput;Delays;Data centers;Load modeling;Bandwidth;Data center;load balancing;multipath},
  doi={10.1109/TNET.2021.3088276}}

@ARTICLE{PDLB,
	author = {Gao, Weimin and Huang, Jiawei and Li, Zhaoyi and Zou, Shaojun and Wang, Jianxin},
	title = {PDLB: Path Diversity-aware Load Balancing with adaptive granularity in data center networks},
	year = {2023},
	journal = {Journal of Cloud Computing},
	volume = {12},
	number = {1},
	doi = {10.1186/s13677-023-00548-x},
	url = {https://www.scopus.com/inward/record.uri?eid=2-s2.0-85178962286&doi=10.1186%2fs13677-023-00548-x&partnerID=40&md5=45f9387e3585547f6c686670796380a4},
	type = {Article},
	publication_stage = {Final},
	source = {Scopus},
	note = {Cited by: 1; All Open Access, Gold Open Access, Green Open Access}
}

@article{HG,
  title={HG: Leveraging Hybrid Switching Granularity to Balance Heterogeneous Data Center Traffic Load for Cloud-Based Industrial Applications},
  author={Zhang, Tao and He, Shengli and Zeng, Xiao and Wu, Xin and Jin, Ku and Hu, Yuanzhen and Ruan, Chang and Zou, Shaojun and Hu, Jinbin and Li, Fangmin},
  journal={IEEE Transactions on Industrial Informatics},
  year={2024},
  publisher={IEEE}
}

@inproceedings{ConWeave,
author = {Song, Cha Hwan and Khooi, Xin Zhe and Joshi, Raj and Choi, Inho and Li, Jialin and Chan, Mun Choon},
title = {Network Load Balancing with In-network Reordering Support for RDMA},
year = {2023},
isbn = {9798400702365},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/3603269.3604849},
doi = {10.1145/3603269.3604849},
booktitle = {Proceedings of the ACM SIGCOMM 2023 Conference},
pages = {816–831},
numpages = {16},
keywords = {network load balancing, programmable network, in-network packet reordering, programmable switches, RDMA, P4},
location = {New York, NY, USA},
series = {ACM SIGCOMM '23}
}

@article{LBT,
title = {Load balancing for heterogeneous traffic in datacenter networks},
journal = {Journal of Network and Computer Applications},
volume = {217},
pages = {103692},
year = {2023},
issn = {1084-8045},
doi = {https://doi.org/10.1016/j.jnca.2023.103692},
url = {https://www.sciencedirect.com/science/article/pii/S108480452300111X},
author = {Jin Wang and Shuying Rao and Ying Liu and Pradip {Kumar Sharma} and Jinbin Hu},
keywords = {Data center, Load balancing, Switching granularity, Multipath}
}

@inproceedings{CONGA,
author = {Alizadeh, Mohammad and Edsall, Tom and Dharmapurikar, Sarang and Vaidyanathan, Ramanan and Chu, Kevin and Fingerhut, Andy and Lam, Vinh The and Matus, Francis and Pan, Rong and Yadav, Navindra and Varghese, George},
title = {CONGA: distributed congestion-aware load balancing for datacenters},
year = {2014},
isbn = {9781450328364},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/2619239.2626316},
doi = {10.1145/2619239.2626316},
booktitle = {Proceedings of the 2014 ACM Conference on SIGCOMM},
pages = {503–514},
numpages = {12},
keywords = {datacenter fabric, distributed, load balancing},
location = {Chicago, Illinois, USA},
series = {SIGCOMM '14}
}

@inproceedings{LetFlow,
author = {Vanini, Erico and Pan, Rong and Alizadeh, Mohammad and Taheri, Parvin and Edsall, Tom},
title = {Let it flow: resilient asymmetric load balancing with flowlet switching},
year = {2017},
isbn = {9781931971379},
publisher = {USENIX Association},
address = {USA},
booktitle = {Proceedings of the 14th USENIX Conference on Networked Systems Design and Implementation},
pages = {407–420},
numpages = {14},
location = {Boston, MA, USA},
series = {NSDI'17}
}

@inproceedings{DRILL,
author = {Ghorbani, Soudeh and Yang, Zibin and Godfrey, P. Brighten and Ganjali, Yashar and Firoozshahian, Amin},
title = {DRILL: Micro Load Balancing for Low-latency Data Center Networks},
year = {2017},
isbn = {9781450346535},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/3098822.3098839},
doi = {10.1145/3098822.3098839},
booktitle = {Proceedings of the Conference of the ACM Special Interest Group on Data Communication},
pages = {225–238},
numpages = {14},
keywords = {Traffic engineering, Microbursts, Load balancing, Datacenters, Clos},
location = {Los Angeles, CA, USA},
series = {SIGCOMM '17}
}

@article{Presto,
author = {He, Keqiang and Rozner, Eric and Agarwal, Kanak and Felter, Wes and Carter, John and Akella, Aditya},
title = {Presto: Edge-based Load Balancing for Fast Datacenter Networks},
year = {2015},
issue_date = {October 2015},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
volume = {45},
number = {4},
issn = {0146-4833},
url = {https://doi.org/10.1145/2829988.2787507},
doi = {10.1145/2829988.2787507},
journal = {SIGCOMM Comput. Commun. Rev.},
month = aug,
pages = {465–478},
numpages = {14},
keywords = {load balancing, software-defined networking}
}

@inproceedings{Hula,
author = {Katta, Naga and Hira, Mukesh and Kim, Changhoon and Sivaraman, Anirudh and Rexford, Jennifer},
title = {HULA: Scalable Load Balancing Using Programmable Data Planes},
year = {2016},
isbn = {9781450342117},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/2890955.2890968},
doi = {10.1145/2890955.2890968},
booktitle = {Proceedings of the Symposium on SDN Research},
articleno = {10},
numpages = {12},
keywords = {Scalability, Programmable Switches, Network Congestion, In-Network Load Balancing},
location = {Santa Clara, CA, USA},
series = {SOSR '16}
}

@INPROCEEDINGS{RPS,
  author={Dixit, Advait and Prakash, Pawan and Hu, Y. Charlie and Kompella, Ramana Rao},
  booktitle={2013 Proceedings IEEE INFOCOM}, 
  title={On the impact of packet spraying in data center networks}, 
  year={2013},
  volume={},
  number={},
  pages={2130-2138},
  keywords={Spraying;Throughput;Topology;Network topology;Bandwidth;Servers;Kernel},
  doi={10.1109/INFCOM.2013.6567015}}

@INPROCEEDINGS{IDEAL,
  author={Guo, Junjie and Yang, Chungang and Li, Fuqiang and Dong, Ru and Song, Yanbo and Kou, Shiwen},
  booktitle={2023 6th World Conference on Computing and Communication Technologies (WCCCT)}, 
  title={IDEAL: Intent Driven Emerging Anti-congestion Router with Load-Balance for SDN}, 
  year={2023},
  volume={},
  number={},
  pages={39-45},
  keywords={Knowledge engineering;Switches;Manuals;Routing;Load management;Stability analysis;Routing protocols;anti-congestion router;deep learning;intent-driven network;network management},
  doi={10.1109/WCCCT56755.2023.********}}

@INPROCEEDINGS{INTLABEL,
  author={Song, Enge and Pan, Tian and Jia, Chenhao and Cao, Wendi and Zhang, Jiao and Huang, Tao and Liu, Yunjie},
  booktitle={IEEE INFOCOM 2021 - IEEE Conference on Computer Communications}, 
  title={INT-label: Lightweight In-band Network-Wide Telemetry via Interval-based Distributed Labelling}, 
  year={2021},
  volume={},
  number={},
  pages={1-10},
  keywords={Data centers;Time-frequency analysis;Network topology;Bandwidth;Computer architecture;Topology;Frequency measurement},
  doi={10.1109/INFOCOM42981.2021.9488799}}

@INPROCEEDINGS{Hermes,
  author={Bera, Rahul and Kanellopoulos, Konstantinos and Balachandran, Shankar and Novo, David and Olgun, Ataberk and Sadrosadat, Mohammad and Mutlu, Onur},
  booktitle={2022 55th IEEE/ACM International Symposium on Microarchitecture (MICRO)}, 
  title={Hermes: Accelerating Long-Latency Load Requests via Perceptron-Based Off-Chip Load Prediction}, 
  year={2022},
  volume={},
  number={},
  pages={1-18},
  keywords={Program processors;Microarchitecture;Prefetching;Source coding;Memory management;Bandwidth;System-on-chip;Microarchitecture;Prefetcher;Caching;Perceptron},
  doi={10.1109/MICRO56248.2022.00015}}

@INPROCEEDINGS{FlyingAdhoc,
  author={Li, Tong and Yang, Chungang and Yang, Lingli},
  booktitle={2022 International Wireless Communications and Mobile Computing (IWCMC)}, 
  title={Intent-Driven QoS-Aware Routing Management for Flying Ad hoc Networks}, 
  year={2022},
  volume={},
  number={},
  pages={1172-1177},
  keywords={Wireless communication;Automation;Network topology;Quality of service;Routing;Autonomous aerial vehicles;Ad hoc networks;FANET;intent-driven network;policy;routing management},
  doi={10.1109/IWCMC55113.2022.9824766}}
