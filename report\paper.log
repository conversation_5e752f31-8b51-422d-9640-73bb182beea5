This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.4.17)  27 JUN 2025 19:56
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**paper
(./paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(./IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count184
\@IEEEtrantmpcountB=\count185
\@IEEEtrantmpcountC=\count186
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1090.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count187
\c@subsection=\count188
\c@subsubsection=\count189
\c@paragraph=\count190
\c@IEEEsubequation=\count191
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count192
\c@table=\count193
\@IEEEeqnnumcols=\count194
\@IEEEeqncolcnt=\count195
\@IEEEsubeqnnumrollback=\count196
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count197
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count198
\@IEEEtranrubishbin=\box52
)
** ATTENTION: Overriding command lockouts (line 2).
(f:/dev/texlive/2024/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen164
)) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen165
) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count199
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count266
\leftroot@=\count267
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count268
\DOTSCASE@=\count269
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen166
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count270
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count271
\dotsspace@=\muskip16
\c@parentequation=\count272
\dspbrk@lvl=\count273
\tag@help=\toks19
\row@=\count274
\column@=\count275
\maxfields@=\count276
\andhelp@=\toks20
\eqnshift@=\dimen167
\alignsep@=\dimen168
\tagshift@=\dimen169
\tagwidth@=\dimen170
\totwidth@=\dimen171
\lineht@=\dimen172
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (f:/dev/texlive/2024/texmf-dist/tex/latex/algorithms/algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (f:/dev/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
\c@ALC@unique=\count277
\c@ALC@line=\count278
\c@ALC@rem=\count279
\c@ALC@depth=\count280
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen173
\Gin@req@width=\dimen174
) (f:/dev/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
) (f:/dev/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (f:/dev/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count281
\l__pdf_internal_box=\box55
\g__pdf_backend_object_int=\count282
\g__pdf_backend_annotation_int=\count283
\g__pdf_backend_link_int=\count284
)
No file paper.aux.
\openout1 = `paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 12.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 12.
(f:/dev/texlive/2024/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 12.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 12.

-- Lines per column: 56 (exact).

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 25.

LaTeX Font Info:    Trying to load font information for U+msa on input line 26.
(f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 26.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 31.


LaTeX Font Warning: Font shape `TU/ptm/m/sc' undefined
(Font)              using `TU/ptm/m/n' instead on input line 35.


Underfull \hbox (badness 7576) in paragraph at lines 83--84
[]\TU/ptm/bx/it/10 Common techniques like Equal-Cost Multi-Path
 []

[1


]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 96.


LaTeX Warning: Citation `rikhtegar2021bite' on page 2 undefined on input line 96.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 96.


LaTeX Warning: Citation `he2021joint' on page 2 undefined on input line 96.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 96.


LaTeX Warning: Citation `pathan2024priority' on page 2 undefined on input line 96.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 98.


LaTeX Warning: Citation `shu2023dynamic' on page 2 undefined on input line 98.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 98.


LaTeX Warning: Citation `guohao2020data' on page 2 undefined on input line 98.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 100.


LaTeX Warning: Citation `irteza2018efficient' on page 2 undefined on input line 100.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 100.


LaTeX Warning: Citation `zheng2019dynamic' on page 2 undefined on input line 100.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 100.


LaTeX Warning: Citation `chung2021dynamic' on page 2 undefined on input line 100.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 102.


LaTeX Warning: Citation `alawadi2022oddlab' on page 2 undefined on input line 102.

[2]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 107.


LaTeX Warning: Citation `fi13020054' on page 3 undefined on input line 107.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 107.


LaTeX Warning: Citation `yan2023st' on page 3 undefined on input line 107.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 107.


LaTeX Warning: Citation `ruelas2018load' on page 3 undefined on input line 107.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 109.


LaTeX Warning: Citation `chang2023learned' on page 3 undefined on input line 109.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 109.


LaTeX Warning: Citation `liu2022bulb' on page 3 undefined on input line 109.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 111.


LaTeX Warning: Citation `hmmlb' on page 3 undefined on input line 111.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 111.


LaTeX Warning: Citation `babayigit2021deep' on page 3 undefined on input line 111.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 113.


LaTeX Warning: Citation `rikhtegar2021deeprlb' on page 3 undefined on input line 113.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 113.


LaTeX Warning: Citation `tosounidis2020deep' on page 3 undefined on input line 113.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 115.


LaTeX Warning: Citation `xiangyun2021deep' on page 3 undefined on input line 115.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 115.


LaTeX Warning: Citation `fancy2021intelligence' on page 3 undefined on input line 115.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 115.


LaTeX Warning: Citation `hao2023link' on page 3 undefined on input line 115.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 117.


LaTeX Warning: Citation `lin2019rilnet' on page 3 undefined on input line 117.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 117.


LaTeX Warning: Citation `lim2021reinforcement' on page 3 undefined on input line 117.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 117.


LaTeX Warning: Citation `liu2021drl' on page 3 undefined on input line 117.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 125.


LaTeX Warning: Citation `LetFlow' on page 3 undefined on input line 125.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 125.


LaTeX Warning: Citation `CONGA' on page 3 undefined on input line 125.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 125.


LaTeX Warning: Citation `Hula' on page 3 undefined on input line 125.

[3]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 127.


LaTeX Warning: Citation `RPS' on page 4 undefined on input line 127.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 127.


LaTeX Warning: Citation `DRILL' on page 4 undefined on input line 127.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 127.


LaTeX Warning: Citation `Presto' on page 4 undefined on input line 127.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 127.


LaTeX Warning: Citation `DLB' on page 4 undefined on input line 127.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 129.


LaTeX Warning: Citation `QLLB' on page 4 undefined on input line 129.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 129.


LaTeX Warning: Citation `BLEND' on page 4 undefined on input line 129.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 129.


LaTeX Warning: Citation `CAFT' on page 4 undefined on input line 129.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 129.


LaTeX Warning: Citation `DFFR' on page 4 undefined on input line 129.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 129.


LaTeX Warning: Citation `CAF' on page 4 undefined on input line 129.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 131.


LaTeX Warning: Citation `MATCP' on page 4 undefined on input line 131.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 131.


LaTeX Warning: Citation `OPLB' on page 4 undefined on input line 131.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 131.


LaTeX Warning: Citation `DDMP' on page 4 undefined on input line 131.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 131.


LaTeX Warning: Citation `MDTLB' on page 4 undefined on input line 131.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 131.


LaTeX Warning: Citation `HPLB' on page 4 undefined on input line 131.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 134.


LaTeX Warning: Citation `FAMG' on page 4 undefined on input line 134.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 134.


LaTeX Warning: Citation `FlowDecider' on page 4 undefined on input line 134.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 134.


LaTeX Warning: Citation `IntFlow' on page 4 undefined on input line 134.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 136.


LaTeX Warning: Citation `AG' on page 4 undefined on input line 136.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 136.


LaTeX Warning: Citation `TLB' on page 4 undefined on input line 136.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 136.


LaTeX Warning: Citation `LBT' on page 4 undefined on input line 136.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 138.


LaTeX Warning: Citation `BOBBLE' on page 4 undefined on input line 138.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 138.


LaTeX Warning: Citation `ILB' on page 4 undefined on input line 138.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 140.


LaTeX Warning: Citation `PDLB' on page 4 undefined on input line 140.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 140.


LaTeX Warning: Citation `HG' on page 4 undefined on input line 140.

[4]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 142.


LaTeX Warning: Citation `ConWeave' on page 5 undefined on input line 142.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 147.


LaTeX Warning: Citation `INTLABEL' on page 5 undefined on input line 147.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 150.


LaTeX Warning: Citation `Hermes' on page 5 undefined on input line 150.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 153.


LaTeX Warning: Citation `FlyingAdhoc' on page 5 undefined on input line 153.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 156.


LaTeX Warning: Citation `IDEAL' on page 5 undefined on input line 156.

No file paper.bbl.

** Conference Paper **
Before submitting the final camera ready copy, remember to:

 1. Manually equalize the lengths of two columns on the last page
 of your paper;

 2. Ensure that any PostScript and/or PDF output post-processing
 uses only Type 1 fonts and that every step in the generation
 process uses the appropriate paper size.

[5] (./paper.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: There were undefined references.

 ) 
Here is how much of TeX's memory you used:
 3810 strings out of 474773
 60364 string characters out of 5759509
 1949842 words of memory out of 5000000
 25944 multiletter control sequences out of 15000+600000
 562958 words of font info for 67 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 57i,8n,65p,1102b,261s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on paper.pdf (5 pages).
