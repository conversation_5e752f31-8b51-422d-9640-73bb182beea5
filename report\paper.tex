\documentclass[conference]{IEEEtran}
\IEEEoverridecommandlockouts
% The preceding line is only needed to identify funding in the first footnote. If that is unneeded, please comment it out.
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\begin{document}

\title{A Survey on Load Balancing in Data Center}

\author{\IEEEauthorblockN{Yikai Huang}
\IEEEauthorblockA{**********}
\and
\IEEEauthorblockN{Xuanyang Huang}
\IEEEauthorblockA{**********}
}

\maketitle

\begin{abstract}
This document is a model and instructions for \LaTeX.
This and the IEEEtran.cls file define the components of your paper [title, text, heads, etc.]. *CRITICAL: Do Not Use Symbols, Special Characters, Footnotes, 
or Math in Paper Title or Abstract.
\end{abstract}

\begin{IEEEkeywords}
component, formatting, style, styling, insert
\end{IEEEkeywords}

\section{Background}
As data center networks (DCNs) have grown in scale and complexity to support modern information society, the effective management of network traffic has become paramount. The performance and efficiency of these networks, which form the backbone for cloud computing, big data analytics, and artificial intelligence, are critical. Load balancing has emerged as a key technology within the field of traffic engineering to optimize the performance of these crucial systems.

\subsection{Defining Load Balancing}
In the context of a Data Center Network (DCN), load balancing refers to the intelligent and equitable distribution of network traffic across multiple available paths or links. The primary goal is to avoid overloading certain links—which may become bottlenecks—while others remain underutilized. By balancing the load, we aim to maximize overall throughput, improve resource utilization, and minimize communication latency.

Formally, traffic scheduling in a capacity-constrained network is often modeled as a Multi-Commodity Flow (MCF) problem. Consider a network represented as a graph $G(V, E)$, where $V$ denotes the set of nodes and $E$ the set of links. Each link $(u, v) \in E$ has an associated capacity $c(u, v)$. There are $K$ flows to be transmitted in the network. Each flow $F_k(s_k, t_k, d_k)$ is defined by a source node $s_k$, a destination node $t_k$, and a demand $d_k$.

Let $f_k(u, v)$ represent the portion of flow $k$ that traverses the link $(u, v)$. Then, the link utilization $\ell(u, v)$ is defined as:
\begin{equation}
    \ell(u, v) = \frac{ \sum_{k=1}^{K} f_k(u, v) }{ c(u, v) }
\end{equation}

The MCF formulation must satisfy the following constraints:

\begin{itemize}
    \item Capacity Constraint: The total flow on any link must not exceed its capacity:
    \begin{equation}
        \sum_{k=1}^{K} f_k(u, v) \le c(u, v), \quad \forall (u, v) \in E
    \end{equation}

    \item Flow Conservation: For any intermediate node $u \notin \{s_k, t_k\}$, the incoming flow must equal the outgoing flow:
    \begin{equation}
        \sum_{w \in V} f_k(w, u) = \sum_{q \in V} f_k(u, q), \quad \forall k, \, u \ne s_k, t_k
    \end{equation}

    \item Demand Satisfaction: The total flow sent from source to destination must equal the demand:
    \begin{equation}
        \sum_{w \in V} f_k(s_k, w) = d_k \quad \Leftrightarrow \quad \sum_{w \in V} f_k(w, t_k) = d_k
    \end{equation}
\end{itemize}

Under this framework, the objective of load balancing is to minimize the maximum link utilization, thereby achieving a more even traffic distribution:
\begin{equation}
    \min \max_{(u, v) \in E} \ell(u, v) = \min \max_{(u,v) \in E} \left( \frac{ \sum_{k=1}^{K} f_k(u, v) }{ c(u, v) } \right)
\end{equation}

Alternatively, in fixed-rate symmetric networks, some studies define the goal as minimizing the utilization gap between the most and least loaded links:
\begin{equation}
    \min \left( \max_{(u,v) \in E} \ell(u, v) - \min_{(u,v) \in E} \ell(u, v) \right)
\end{equation}

However, solving the MCF problem optimally is known to be NP-hard, making exact solutions impractical for large-scale networks. Consequently, numerous heuristic and adaptive load balancing approaches have been proposed, which are reviewed in the subsequent sections.


\subsection{Challenges in Data Center Load Balancing}
At its core, a data center network (DCN) is in a constant struggle to manage resource contention. The traffic it handles is not uniform; it's a volatile mix of small, latency-sensitive "mice flows" and massive, bandwidth-hungry "elephant flows". On top of this, networks must contend with sudden, high-volume "micro-bursts" which are transient and difficult to predict, yet can instantly create congestion. This inherent chaos means that without intelligent management, some network paths will inevitably become overwhelmed, creating bottlenecks that degrade the performance of the entire system.

Common techniques like Equal-Cost Multi-Path (ECMP) use hashing to assign flows to specific paths. While simple, this approach is blind to the actual size of the flows. As a result, a "hash collision" can occur by pure chance, directing several large elephant flows to the exact same link. This creates severe, localized congestion on that one path, while other perfectly viable links sit underutilized, thereby defeating the purpose of load balancing.

Furthermore, attempts to achieve more precise control by splitting traffic at finer granularities introduce a difficult paradox. While distributing traffic on a per-packet basis offers maximum flexibility and resource utilization , it significantly increases the likelihood of packet reordering—where packets arrive at the destination in a different sequence than they were sent. Standard transport protocols often interpret this reordering as packet loss and trigger unnecessary data retransmissions. This reaction not only increases flow completion time but also wastes bandwidth by sending redundant data, adding to the very network load that the system was trying to alleviate.

Finally, these challenges are compounded by the physical and logical instability of the network itself. Network topologies can become asymmetric due to equipment configuration problems or link failures, resulting in imbalanced bandwidth between nodes. Such asymmetry can render some paths less capable than others, and if a load balancing scheme is not aware of this imbalance, it may continue to route traffic down these degraded paths, causing severe bottlenecks and impacting overall network performance. Addressing these multifaceted issues is essential for designing the effective and robust load balancing strategies that modern data centers require.

\section{Schemes of Load Balancing}
Load balancing schemes are broadly categorized into two main approaches: centralized and distributed, each with distinct operational principles and characteristics.

\subsection{Centralized Mechanisms}
Centralized load balancing schemes rely on a central controller that gathers and processes global network state information. This approach enables globally optimal scheduling decisions by providing a comprehensive perspective on the entire network. Controllers, typically equipped with substantial processing capabilities, aggregate vast amounts of data, such as traffic matrices and link utilization metrics. The integration of artificial intelligence, particularly machine learning and reinforcement learning, has become increasingly prevalent, allowing these schemes to adaptively learn and predict network states to guide decisions. While offering a holistic view, centralized schemes can face challenges with control overhead and scalability, especially when handling numerous small flows where each decision may require communication with the controller.

\subsubsection{Conventional schemes}
BiTE\cite{rikhtegar2021bite}, a dynamic bi-level traffic engineering scheme for SDN-based DCNs, models the problem with a traffic routing module (leader) and an energy-saving module (follower). Solved by a co-evolutionary metaheuristic, BiTE successfully performs traffic load balancing while preserving energy efficiency. Similarly, ESLB\cite{he2021joint} focuses on the synergy between energy and load balancing by scheduling flows to minimize activated devices and using multipath routing on paths with the minimum average link utilization. Its heuristic algorithm demonstrates high efficiency and superior performance in achieving these dual goals. The challenge of optimal routing is also addressed by Pathan\cite{pathan2024priority}, which employs a Mixed Integer Linear Programming (MILP) formulation and provides two practical greedy algorithms (PEMA and PEDL) that jointly consider flow priority, path energy, and load distribution, leading to significant improvements in successful flow ratio, energy savings, and load balancing.

A common focus in centralized schemes is the management of large elephant flows, which can disproportionately cause congestion. The DMLBP\cite{shu2023dynamic} algorithm uses an enhanced Particle Swarm Optimization (PSO) with a Metropolis acceptance criterion to dynamically assign optimal multiple paths for these large streams, improving bandwidth utilization and network throughput. In a similar vein, IALB\cite{guohao2020data} leverages an enhanced artificial bee colony algorithm to find optimal paths for elephant flows. By redefining its fitness function to include the number of large flows, real-time link load, and link stability, IALB demonstrates superior network throughput and link utilization over traditional ECMP and Hedera.

Some approaches are tailored to specific DCN topologies or hardware capabilities. Addressing the poor performance of packet spraying in asymmetric topologies, SAPS\cite{irteza2018efficient} creates a symmetric virtual topology for each flow, mapping flows intelligently based on their needs. This method consistently outperforms state-of-the-art schemes in flow completion times across various workloads and failure scenarios. Zheng et al.\cite{zheng2019dynamic} propose a two-stage greedy algorithm that jointly optimizes flow routing and the configuration of adjustable optical links for topologies like Diamond, VL2, and BCube, significantly reducing traffic congestion. Specifically for BCube DCNs, Chung et al.\cite{chung2021dynamic} introduce CDPFS (single-path) and CDPFSMP (multi-path) algorithms. These leverage a central master to identify the least congested paths and allocate them in parallel, effectively mitigating flow collisions and outperforming the distributed BCube Source Routing (BSR) algorithm.

Bridging proactive and reactive control, Oddlab\cite{alawadi2022oddlab} utilizes a hybrid flow scheduling approach. It combines proactive ECMP hashing with adaptive routing decisions made by a centralized SDN controller based on data plane statistics. A core innovation is its faulty link detection mechanism, which temporally correlates network utilization states to identify failures. Experiments show that Oddlab effectively reduces flow completion time and accurately identifies faulty links while maximizing bisection bandwidth.

\subsubsection{Machine learning based schemes}
A growing trend in centralized load balancing involves the integration of machine learning (ML) to learn and adapt to dynamic network conditions, moving beyond pre-defined heuristics. These methods leverage data to predict future states or learn optimal control policies.

One category of ML-based methods focuses on prediction. The LLBR algorithm\cite{fi13020054} leverages a modified Spatio-Temporal Residual Network (ST-ResNet)\cite{yan2023st} to forecast link occupancy and utilization in the upcoming duty cycle. This proactive capability allows for the selection of optimal routes based on future conditions, significantly reducing transmission time and packet loss. Similarly, the ANN-based approach by Ruelas et al.\cite{ruelas2018load} uses an Artificial Neural Network to predict network performance from real-time bandwidth and latency measurements, enabling the selection of the least loaded path and achieving significant performance gains.

Beyond pure prediction, deep supervised learning is employed by LLB\cite{chang2023learned} to automatically generate instance-optimal Weighted ECMP (WCMP) weights. It learns to adapt to real-time traffic patterns, network asymmetries, and failures, offering a fast and deployable solution on modern SmartNICs. To overcome the high latency of direct Deep Reinforcement Learning (DRL) for per-flow decisions, BULB\cite{liu2022bulb} adopts a hybrid strategy. It uses an offline-trained DRL agent to find optimal link weights and then employs imitation learning to translate the complex neural network into a lightweight decision tree for online deployment, achieving better flow completion times and lower decision latency.

Other ML paradigms are also explored. HMMLB\cite{hmmlb} creatively transforms path selection into a Hidden Markov Model (HMM) problem, using a few monitored links as observable states to infer the optimal path. This reduces the time cost of path selection while maintaining high throughput. Babayigit et al.\cite{babayigit2021deep} introduce a Deep Learning (DL) model for SDN-based DCNs, which is trained on link load values to determine optimal paths. Results show this DL approach outperforms other ML classifiers like ANN, SVM, and Logistic Regression in response time and accuracy.

Deep Reinforcement Learning (DRL) has emerged as a particularly powerful technique, enabling agents to learn optimal control policies through direct interaction with the network environment. For example, DeepRLB\cite{rikhtegar2021deeprlb} uses the Deep Deterministic Policy Gradient (DDPG) algorithm to adaptively learn optimal link-weight values, with its convolutional model (DeepRLB-Conv) showing considerable performance gains over ECMP. In another approach, Tosounidis et al.\cite{tosounidis2020deep} integrate a Deep Q-Learning (DQL) model with a CNN, where the DQL agent acts as the SDN controller to distribute service requests, achieving higher throughput and lower latency compared to traditional algorithms.

To better capture the topological structure of the network, GDLB\cite{xiangyun2021deep} integrates Graph Convolutional Networks (GCN) with DRL. This allows the model to intelligently perceive topology and traffic states, making superior routing decisions that optimize for both load balancing and QoS metrics. Q-learning is also applied to enhance control-plane efficiency and manage elephant flows. SPRLN\cite{fancy2021intelligence} uses a Q-learning algorithm to dynamically select paths by monitoring status and predicting congestion, thereby avoiding abnormal polling to the controller and improving overall network performance. Meanwhile, EDDQN\cite{hao2023link} employs Deep Q-learning (DQN) specifically to reroute identified elephant flows after a two-stage detection process, enhancing link utilization and throughput.

Finer-grained control is another area of focus for DRL. RILNET\cite{lin2019rilnet} applies Deep DPG for routing decisions at the flowlet level, allowing it to distribute traffic more effectively and mitigate bottlenecks. Similarly, RWCMP\cite{lim2021reinforcement} is a reinforcement learning-based algorithm that learns optimal traffic split ratios for egress ports at the flowlet-level, demonstrating consistently lower network utilization across various traffic patterns and network topologies. Finally, DRL-PLink\cite{liu2021drl} applies DRL to a different aspect of the problem: dynamically allocating bandwidth to private-links established for different flow types. This scheme effectively optimizes mix-flow scheduling, leading to a significant decrease in average flow completion time and improved load balance.



\subsection{Distributed Mechanisms}
In distributed load balancing technology, all nodes jointly participate in task scheduling or resource allocation, and each node communicates by maintaining a local information base, thereby efficiently completing the allocation and reallocation of tasks. This type of solution runs directly on the host or switch of the data plane, and can make fast load balancing decisions without the intervention of the control plane. It has good scalability and supports finer-grained traffic scheduling. Each node can autonomously select a path locally and quickly respond to traffic bursts within the data center, but it also faces difficulties such as collecting global congestion information and limitations in responding to topology changes such as link failures and switch crashes, especially in symmetric network architectures. According to the different scheduling granularities, this type of method can be divided into single granularity and mixed granularity.

\subsubsection{single granularity schemes}
Flowlet-based strategies have gained prominence due to their ability to balance load effectively while minimizing packet reordering. In this category, LetFlow\cite{LetFlow} achieves decentralized flowlet scheduling by leveraging inter-packet timing gaps, enabling switches to route each segment independently. Without requiring synchronized clocks or central orchestration, it adapts well to traffic asymmetry. Similarly, CONGA\cite{CONGA} enhances flowlet-level routing through coordinated local feedback among ToR switches, offering near-real-time congestion-aware decisions within leaf-spine topologies. Hula\cite{Hula} further pushes the boundary by incorporating in-network telemetry to estimate downstream congestion, selecting paths for new flowlets based on live queue measurements. While each employs a different mechanism for path selection, they all capitalize on the benefits of per-flowlet granularity to achieve high throughput and responsiveness under dynamic conditions.

A different group of approaches emphasizes packet-level or flowcell-level load balancing to achieve even finer control. RPS\cite{RPS} exemplifies a stateless design where packets are randomly sprayed across available paths, offering simplicity and full path diversity at the expense of significant reordering. To improve upon this, DRILL\cite{DRILL} introduces probabilistic path selection guided by local congestion feedback, maintaining minimal state while enhancing adaptability. At the sender side, Presto\cite{Presto} adopts flowcells—equal-sized chunks dispatched via ECMP-like mechanisms—which reduce coordination needs while improving balance. DLB\cite{DLB} also leverages flowcell-level segmentation, dynamically routing chunks based on local telemetry, ensuring compatibility with existing switch hardware. These methods favor minimal central coordination and are well-suited for high-speed environments where reactivity and granularity matter more than strict ordering.

Some designs integrate feedback and path probing to optimize for congestion and latency. QLLB\cite{QLLB} relies on switch-local queue occupancy and RTT to reroute flows away from congested paths, offering a lightweight, distributed alternative to centralized schedulers. BLEND\cite{BLEND} and CAFT\cite{CAFT} share a focus on using telemetry signals—such as queue lengths or congestion window estimates—to guide rerouting, either reactively or periodically. While CAFT fuses packet-level metrics with flowlet scheduling to respond quickly to imbalance, BLEND emphasizes path shift smoothness and packet order preservation. DFFR\cite{DFFR} builds on downstream feedback loops to reassign flowlets under long-lived congestion, whereas CAF\cite{CAF} distills telemetry and buffer data into a real-time congestion factor to steer flows. These schemes differ in granularity but share the same principle: local, real-time feedback can enable effective, scalable load balancing without the overhead of global visibility.

Several proposals integrate host-based intelligence with in-network adaptation, leading to hybrid designs that adjust behavior based on traffic characteristics. MA-TCP\cite{MATCP} handles path changes gracefully at the transport layer by distinguishing reordering from actual loss, avoiding unnecessary congestion window reductions. OPLB\cite{OPLB} and DDMP\cite{DDMP} combine initial path probing or classification at the end host with reactive in-network decisions, particularly tailoring forwarding based on flow size or lifespan. MDTLB\cite{MDTLB} further expands this direction by incorporating multi-dimensional telemetry—queue state, link utilization, flow age—into host-side decision logic for incoming flowlets. HPLB\cite{HPLB}, while in-network, also relies on predictive metrics such as expected queuing delay to make smarter routing decisions under heterogeneous conditions. Collectively, these schemes highlight the growing synergy between end-host processing power and fine-grained network feedback.

\subsubsection{mixed granularity schemes}
Some schemes adopt an end-host-driven granularity control strategy, where flows are classified at the source and treated differently based on their anticipated size or urgency. In particular, mechanisms like those used in FAMG\cite{FAMG} and FlowDecider\cite{FlowDecider} identify large flows early and divide them into flowcells before entering the network. These flowcells are then dispatched to ports with lighter load, often chosen by comparing queue lengths across a few randomly selected candidates and the previously used path. At the same time, short flows are transmitted at the packet level to minimize latency. This dual treatment enables fast reactions to congestion and improves flow completion times. Enhancements like IntFlow\cite{IntFlow} further enrich this strategy by embedding flow status—including retransmissions, rate, and delay—into packet headers, allowing programmable switches to decide whether rerouting is needed based on congestion marks or flowlet timing gaps.

Other systems rely primarily on in-network intelligence to guide forwarding granularity decisions dynamically. AG\cite{AG}, for example, monitors one-way delay differences using TCP-level signals to infer topological asymmetry, increasing granularity in asymmetric settings to avoid reordering and decreasing it when conditions permit finer balancing. TLB\cite{TLB} introduces a coordination mechanism between short and long flows: when short flow load is heavy, it enlarges long-flow granularity to reduce competition; otherwise, it enables finer-grained long flow scheduling for better utilization. LBT\cite{LBT} follows a similar philosophy, where flow types are assigned different forwarding behaviors depending on network load intensity, and thresholds are adjusted to match current traffic composition. These schemes ensure that switching granularity is neither fixed nor uniform, but responsive to measured or estimated traffic conditions.

Another category emphasizes responsiveness to path diversity and burstiness. BOBBLE\cite{BOBBLE} detects flow size by examining packet signatures over time and applies packet-level forwarding to short flows for speed, while employing flowlet-level forwarding for larger flows to preserve order. Its refilling mode uses weighted randomization to favor underutilized ports, improving queue balance. Similarly, ILB\cite{ILB} separates coexisting flows to avoid collisions—if small flows encounter long ones on the same path, it forces large flows to reroute, and reclaims idle paths for throughput once short flows finish. These mechanisms prioritize latency-sensitive traffic while still ensuring high aggregate performance.

In the context of topology-aware adaptation, PDLB\cite{PDLB} estimates asymmetry via RTT differences and computes the most suitable flowcell size to avoid excessive reordering while maximizing link usage. Unlike in-network methods, its logic resides entirely at the sender, making it lightweight and deployment-friendly. On the other hand, HG\cite{HG} structures the flow into stages and applies granularity shifts when traffic volume crosses phase thresholds, using a linear weighting model to evaluate path options based on queueing delay and inter-packet timing. These mechanisms offer continuous adaptation without relying solely on flowlet detection.

Finally, for RDMA networks where traditional granularities often fail, ConWeave\cite{ConWeave} employs a cooperative approach: rerouting is cautiously done at the source ToR using RTT feedback, and reordering is handled at the destination ToR via programmable switch capabilities. This design bridges the gap between too-frequent packet-level decisions and inflexible flowlet routing by adapting both routing frequency and granularity to network feedback, showing strong benefits in bursty or asymmetric conditions.

\section{Open problem and insights}
\subsection{Open problem}
\subsubsection{Comprehensive and Timely Network State Awareness}
Current load balancing schemes often depend on limited or delayed feedback signals such as queue length, RTT, or ECN. These metrics, while lightweight, are often insufficient to reflect rapidly shifting congestion in modern data center networks. The lack of fine-grained, real-time visibility across multiple dimensions (e.g., path asymmetry, burstiness, flow priority) leads to suboptimal routing decisions. A critical challenge is to design efficient, low-overhead, and distributed telemetry systems that can provide accurate end-to-end path insights and enable more proactive, adaptive load balancing\cite{INTLABEL}.

\subsubsection{Adaptive Granularity Control under Dynamic Conditions}
Fine-grained load balancing—at the level of packets, flowlets, or flowcells—offers greater flexibility and better path utilization but often leads to packet reordering, increased retransmissions, or inconsistent delivery latency. Conversely, coarse-grained strategies reduce reordering but fail to respond quickly to microbursts or path variation. There remains a key tension between granularity adaptability and delivery consistency, especially under asymmetric topologies and mixed workloads\cite{Hermes}. Future systems must be able to intelligently switch or tune granularity based on runtime conditions, minimizing reordering while maintaining responsiveness.

\subsubsection{Application-Aware and Future-Proof Load Balancing}
Most existing mechanisms focus solely on link-level efficiency, ignoring application-level objectives such as latency constraints, deadline-awareness, or flow dependencies. Additionally, while learning-based scheduling offers adaptive capabilities, it often suffers from poor interpretability, long training cycles, and generalization issues. Furthermore, emerging protocols (e.g., RDMA, QUIC), programmable networks, and multi-tenant cloud-edge architectures pose new constraints that conventional designs fail to address\cite{FlyingAdhoc}. The challenge is to develop load balancing frameworks that are context-aware, protocol-flexible, and safely integrate machine learning, making them robust, explainable, and deployable across diverse environments.

\subsubsection{Energy Efficiency and Intent-Driven Evolution for Emerging Workloads}
As data center networks continue to scale, energy efficiency remains a critical design goal for load balancing, despite receiving less emphasis in recent studies. The growing need to optimize performance under power constraints—especially in distributed and edge data center deployments—calls for holistic load balancing strategies that jointly consider throughput, latency, and energy consumption. At the same time, the rise of emerging workloads, such as large language models (LLMs), introduces new demands on traffic management. These applications generate distinctive flow patterns and often require tailored, dynamic path selection. Recent research\cite{IDEAL} highlights the potential of intent-driven approaches, which translate high-level application goals into actionable load balancing behaviors. This convergence of energy-aware design and intent-based control marks a significant shift from traditional rule-based paradigms toward intelligent, context-aware systems capable of adapting to both infrastructure constraints and application-level semantics.

\subsection{Insights}
\subsubsection{Centralized Solutions Evolve via Tech Integration}
Centralized load balancing algorithms, though constrained by controller scalability, are advancing through programmable network technologies (e.g., OpenFlow, P4) and multi-layer controller architectures. By offloading functions like large flow detection to switches and adopting hierarchical traffic management, these solutions balance global scheduling optimality with reduced control overhead, leveraging hardware programmability to enhance scalability.
\subsubsection{End-Host and Distributed Approaches for Specific Scenarios}
End-host-based load balancing (e.g., MPTCP, Presto) gains traction in small data centers by avoiding hardware upgrades, enabled by virtualization to deploy in hypervisor switches. Meanwhile, distributed schemes are shifting from generic designs (e.g., CONGA) to scenario-specific ones—sacrificing universality for simplicity to optimize traffic patterns in latency/throughput-sensitive applications, aligning with data center demands for low-complexity deployment.
\subsubsection{Simple and Flexible Designs Are the Future}
Given the prevalence of asymmetric network scenarios caused by link failures and the diverse performance goals such as minimizing tail latency or meeting QoS requirements, future load balancing schemes need to maintain robustness with minimal complexity and feedback overhead. Additionally, combining rate control, priority management, and path selection to address multiple optimization objectives simultaneously will enable smarter and more adaptive traffic scheduling in data centers.
\bibliographystyle{IEEEtran} % 这指定了IEEE的参考文献样式
\bibliography{references}    % 这指定了你的.bib文件的名称（不含扩展名）

\end{document}


