## Fat-tree
### 2. Fat-tree 架构的目标与优势

针对上述问题，Fat-tree 架构旨在实现以下目标：
*   **可扩展的互连带宽**：数据中心中任意主机都应能够以其本地网络接口的全带宽与其他任何主机进行通信。
*   **规模经济**：利用廉价的**商用以太网交换机**作为大规模数据中心网络的基础，就像商用 PC 集群取代专用 SMP 和 MPP 一样。
*   **向后兼容性**：整个系统应向后兼容运行以太网和 IP 的主机，无需修改现有的数据中心。

通过将**商用交换机以 Fat-tree 架构互连**，该方法可以实现数万节点的集群的**全对分带宽 (full bisection bandwidth)**。

### 3. Fat-tree 架构的设计细节

*   **基于 Clos 拓扑**：Fat-tree 是一种特殊的 Clos 拓扑。
*   **`k`叉 Fat-tree 结构**：
    *   一个 `k` 叉 Fat-tree 包含 `k` 个 **Pods** (分仓)。
    *   每个 Pod 包含两层 `k/2` 个交换机：**边缘层 (edge layer)** 和 **汇聚层 (aggregation layer)**。
    *   边缘层的每个 `k` 端口交换机直接连接到 `k/2` 个主机。
    *   剩余的 `k/2` 个端口连接到汇聚层中 `k/2` 个 `k` 端口的交换机。
    *   共有 `(k/2)^2` 个 `k` 端口的**核心交换机 (core switches)**。每个核心交换机连接到 `k` 个 Pod 中的每个 Pod。
    *   一个由 `k` 端口交换机构建的 Fat-tree 支持 `k^3/4` 台主机。该论文重点关注 `k=48` 的设计，可支持 27,648 台主机。
*   **同构交换元件**：Fat-tree 拓扑的一个优点是所有交换元件都是相同的，这使得可以利用廉价的商品化部件用于通信架构中的所有交换机。
*   **可重排无阻塞**：Fat-tree 是可重排无阻塞 (rearrangeably non-blocking) 的，这意味着对于任意通信模式，存在一组路径可以饱和拓扑中终端主机的所有可用带宽。

### 4. IP 地址分配和路由机制

为了充分利用 Fat-tree 结构提供的高扇出能力和多路径，需要对传统的 IP 转发功能进行适度修改。
*   **IP 地址分配**：所有 IP 地址都分配在私有 10.0.0.0/8 地址块内。
    *   Pod 交换机地址形式为 `10.pod.switch.1`（switch在[0,k−1]内，从左到右，自下而上）。
    *   核心交换机地址形式为 `10.k.j.i`（j和i表示该交换机在(k/2)²核心交换机网格中的坐标，均在[1,(k/2)]内，从左上角开始）。
    *   主机地址形式为 `10.pod.switch.ID`，其中 ID 是主机在该子网中的位置。
    *   这种方案虽然对地址空间利用率较低，但简化了路由表的构建，并可扩展至 4.2M 主机。

*   **两级路由表的结构与查找过程**

    两级路由表由两部分组成：
    1.  **一级表（前缀表）**：包含**左手（left-handed）前缀**，形式为 `/m` 前缀掩码（例如 `1^m 0^(32-m)`）。这是标准的路由表查找，进行**最长匹配前缀查找**。
    2.  **二级表（后缀表）**：包含**右手（right-handed）后缀**，形式为 `/m` 后缀掩码（例如 `0^(32-m) 1^m`）。当一级表的最长匹配前缀查找结果为**非终止前缀**时，会继续在二级表中查找**最长匹配后缀**。

    **查找过程**：
    *   当一个数据包到达交换机时，首先在**一级表**中进行最长匹配前缀查找。
    *   如果找到的**前缀是终止的**（terminating prefix），则数据包会立即根据该前缀对应的端口转发。
    *   如果找到的**前缀是非终止的**，则会在**二级表**中根据目标IP地址的后缀进行最长匹配后缀查找，并根据匹配到的后缀对应的端口转发。
    *   在硬件实现中，这可以通过**三态内容寻址存储器（TCAM）**来实现，TCAM可以并行搜索所有条目，并支持通配符位（don't care bits），适用于存储可变长度的前缀。尽管TCAM存储密度较低且耗电昂贵，但对于本架构中相对较小的路由表（每表不超过`k`个一级前缀和`k/2`个二级后缀），是可行的。查找延迟只会略微增加，但由于硬件并行性，影响会很小。

* **Pod交换机路由表生成 (Algorithm 1)**

    Pod交换机（包括下层和上层）是流量扩散的关键点。
    *   **Pod内部流量**：对于发送到**同一Pod内但不同子网**的主机的流量，Pod交换机会分配**终止前缀**，直接指向目标子网的交换机。
    *   **Pod间流量**：对于所有**外发Pod间流量**，Pod交换机含有一个**默认的 `/0` 前缀**，并关联一个**二级表**，该二级表根据目标IP地址的**主机ID（最低有效字节）**进行匹配。
    *   通过使用主机ID作为确定性熵的来源，流量能够**均匀地扩散到上行链路，通向核心交换机**。这种机制还能确保发往同一主机的后续数据包遵循相同的路径，从而**避免数据包乱序**。
    *   算法1的第6-9行展示了这一点：为默认 `/0` 前缀添加一个二级表，该表为每个主机ID `i` (`[2, k/2+1]`) 添加一个后缀 `0.0.0.i/8`，并根据 `(i - 2 + z) mod (k/2) + (k/2)` 计算输出端口。这里的取模操作是为了避免来自不同下层交换机且发往具有相同主机ID的主机的流量汇聚到同一个上层交换机。
    *   下层Pod交换机也会执行类似操作，但会省略其自身子网的 `/24` 前缀步骤，因为该子网的流量是在本地交换的。

* **核心交换机路由表生成 (Algorithm 2)**

    核心交换机的路由表相对简单：
    *   由于每个核心交换机都连接到每个Pod（端口 `i` 连接到Pod `i`），核心交换机只包含**终止的 `/16` 前缀**，指向它们的**目标Pod**。
    *   一旦数据包到达核心交换机，到其目标Pod只有**精确的一条链路**。

### 5. 性能、成本与功耗

*   **对分带宽性能**：
    *   传统树形结构在任何跨 Pod 通信模式下，链路饱和导致仅实现约 28% 的理想带宽。
    *   流调度器通过全局知识，在随机通信模式下达到了 93% 的理想对分带宽。
*   **成本效益**：
    *   与传统分层架构相比，Fat-tree 架构显著降低了成本。
*   **功耗与散热**：
    *   尽管 Fat-tree 架构使用更多的单个交换机，但其总功耗和散热优于当前数据中心设计。

### 6. 容错性

Fat-tree 拓扑由于任意主机对之间存在多条可用路径，因此具有很强的容错能力。
*   通过**双向转发检测 (BFD)** 机制，网络中的每个交换机都能检测到链路或相邻交换机的故障。
*   当检测到故障时，交换机可以**在下游一两跳内进行路径绕行**。
*   例如，当下层到上层交换机之间的链路故障时，本地流分类器会将该链路的“成本”设置为无穷大，并选择另一个可用的上层交换机。对于进入上层交换机的跨 Pod 流量，核心交换机也会被通知无法通过该路径传输流量，并相应地调整路由。
*   当上层到核心交换机之间的链路故障时，本地路由表将受影响的链路标记为不可用，并选择另一个核心交换机。核心交换机也会向其他上层交换机广播标签，表明无法将流量传输到整个 Pod。
*   流调度器也可以通过将故障链路标记为忙碌或不可用来适应故障，从而排除包含故障链路的路径。

### 7. 布线与部署考量

*   尽管 Fat-tree 拓扑需要大量电缆来互连所有机器，但通过巧妙的**封装和放置技术**可以缓解这种复杂性。
*   例如，可以将一个 Pod 内的 48 个交换机打包成一个**单一的整体单元 (pod switch)**，拥有 1,152 个面向用户的端口（其中 576 个连接到 Pod 内的机器，另外 576 个连接到核心层交换机），并通过内部布线处理边缘层和汇聚层之间的互连。
*   通过将机架和 Pod 呈二维网格布局，可以减少布线长度并支持电缆标准化。

### 8. 与相关工作的比较

Fat-tree 架构的提出建立在高性能计算和大规模并行处理 (MPP) 领域可扩展互连网络研究的基础上。
*   **MPP 互连**：许多 MPP 互连（如 Thinking Machines 的 CM-5 和 SGI 的 Altix 3000）都采用了 Fat-tree 拓扑。Myrinet 交换机 也使用 Fat-tree 拓扑，并采用基于预定拓扑知识的源路由。
*   **InfiniBand**：InfiniBand 是高性能计算环境中的流行互连技术，也通过 Clos 拓扑变体实现可扩展带宽。然而，InfiniBand 强制执行自己的协议层 1-4，而以太网/IP/TCP 在某些设置中更具吸引力。
*   **Torus 拓扑**：Torus 拓扑（如 BlueGene/L 和 Cray XT3）在 MPP 环境中具有无专用交换元件和简单点对点链路的优点。但在集群环境中，其布线复杂性迅速变得难以承受。
*   **ECMP (Equal-Cost Multi-Path)**：ECMP 提出了轮询、随机化、区域分割和哈希等无状态转发算法。然而，轮询和随机化可能导致数据包重排序。区域分割会导致路由前缀数量爆炸，增加成本和查询延迟。哈希技术不考虑流带宽，可能导致过载订阅。相比之下，Fat-tree 架构的路由技术利用了 Fat-tree 拓扑的特定属性来实现良好的性能。
![alt text](image.png)
![alt text](image-1.png)
![alt text](image-2.png)
端口号：
2 3
0 1


## MPTCP
多路径TCP（Multipath TCP，简称MPTCP）是一种由IETF工作组提出的协议，旨在允许单个数据流在多条路径之间进行拆分传输。这带来了显著的优势，包括：

*   **可靠性**：当一条路径出现故障时，连接可以继续保持。
*   **资源利用效率**：可以更有效地利用网络资源，实现负载均衡。
*   **移动性**：在移动设备上，MPTCP可以同时利用多个无线接口（如WiFi和3G），从而提升用户体验并应对无线网络固有的可变连接性。

然而，实现MPTCP也带来了挑战，特别是如何高效公平地在竞争流之间共享网络容量，以及如何设计鲁棒的拥塞控制算法。

**MPTCP协议**
MPTCP位于传输层中，TCP和套接字之间，其不改变TCP和套接字本身内容，可以为每个MPTCP连接创建多个子流。MPTCP仍然三次握手协议，其在TCP的options字段设置MP_CAPABLE字段（通常没有用）。如果想申请建立MPTCP连接，就设置MP_CAPABLE字段，同时如果对方回应的ACK中也包含MP_CAPABLE字段，那就表面双方都可建立MPTCP连接。连接申请方在回应一个ACK，即可建立一个MPTCP连接，这个连接可称为一个MPTCP的子流。

经过多次MPTCP的握手，就可以建立多个MPTCP子流。同时，这些子流可以同属于一个MPTCP连接。为了识别同一个MPTCP连接的子流，MPTCP给每个子流分配一个独特的token。SYN的MP_JOIN字段包含了识别同属于一个MPTCP连接的token。

**MPTCP拥塞控制算法的设计**

MPTCP的拥塞控制算法是在传统TCP窗口拥塞控制算法的基础上进行改进的。传统TCP在未检测到丢包时增加拥塞窗口，检测到丢包时减半拥塞窗口。MPTCP连接由一组子流（subflows）组成，每个子流可能通过Internet中的不同路由。每个子流维护自己的拥塞窗口。MPTCP发送方根据子流窗口的可用空间将数据包分散到这些子流中。窗口的调整遵循以下规则：

*   **每个子流r收到ACK时**：对于包含路径r的R的每个子集S，计算一个复杂的表达式，然后找到所有此类S的最小值，并将wr（子流r的拥塞窗口）增加该值。
*   **每个子流r发生丢包时**：将窗口wr减半。

该算法旨在最大化性能，同时与现有TCP流量优雅共存。

**MPTCP设计中考虑的关键问题及解决方案**

![alt text](image-3.png)
![alt text](image-4.png)
![alt text](image-5.png)
![alt text](image-6.png)
![alt text](image-8.png)

1.  **共享瓶颈处的公平性**
    *   **问题**：如果简单地在每个子流上运行常规TCP拥塞控制，多路径流将获得比单路径流多一倍的吞吐量（假设所有RTT相等），这是不公平的。
    *   **EWTCP (Equal-Weight TCP) 算法**：尝试通过对每个ACK增加窗口wr乘以一个系数`a/wr`来解决公平性问题，其中`a = 1/√n`（n为路径数量）。在所有RTT相等的情况下，这使得多路径流获得与常规TCP在瓶颈链路相同的吞吐量。
    *   **COUPLED 算法**：进一步发展，其核心思想是多路径流应将其所有流量转移到拥塞程度最低的路径上。它通过在每个ACK时将窗口wr增加`1/wtotal`（wtotal是所有子流的总窗口大小）来实现。在丢包时，将wr减少`wtotal/2`。COUPLED算法在丢包率不相等的情况下，会将流量转移到丢包率最低的路径上，从而使得拥塞平衡。

2.  **选择高效路径**
    *   **问题**：虽然EWTCP对常规TCP流量公平，但它可能无法高效利用网络，例如在存在不同跳数的路径时，它不会优先使用较短、拥塞较小的路径。
    *   **COUPLED 算法**：通过将流量转移到拥塞程度较低的路径来解决此问题，从而实现高效的资源分配。这有助于平衡网络中的丢包率。

3.  **RTT不匹配问题**
    *   **问题**：EWTCP和COUPLED在RTT不相等时都会出现问题。例如，在WiFi和3G并存的场景中，3G路径通常有长延迟和低丢包率，而WiFi路径可能有较短延迟和较高丢包率。COUPLED会将所有流量发送到拥塞程度较低的路径（可能导致选择较慢的3G），而EWTCP则会平均分配，导致总吞吐量都不理想。
    *   **MPTCP的解决方案**：本文提出的MPTCP算法通过引入RTT补偿机制来解决这个问题。它提出了两个公平性要求：
        *   多路径流应至少获得其最佳单路径TCP所能获得的吞吐量。
        *   多路径流在任何路径或路径集合上所占用的容量不应超过如果它是使用该路径集合中最佳路径的单路径TCP流所占用的容量。
        *   算法通过限制窗口增加（`min(a/wtotal, 1/wr)`）来确保多路径流在任何路径上的容量不超过单路径TCP流，同时偏向拥塞程度较低的路径。

4.  **适应负载变化**
    *   **问题**：COUPLED算法存在一个陷阱，即使所有子流的RTT相同也会出现。如果一条路径上的竞争流量终止，COUPLED会将所有流量转移到该路径上，但可能“被困住”，因为它不再在另一条路径上发送流量，因此无法探测并发现另一条路径何时改善。
    *   **SEMICOUPLED 算法**：作为折衷，它在每个ACK时将窗口wr增加`a/wtotal`，丢包时将wr减半`wr/2`。该算法尝试在每条路径上保持适度的流量，同时仍然偏向拥塞程度较低的路径，避免了“被困”的问题。

**协议实现考量**

虽然研究主要关注拥塞控制，但MPTCP的协议修改也很精妙，需要避免死锁，特别是在缓冲区管理和流控制方面。

*   **子流建立**：通过TCP选项协商多路径使用，并允许后续子流加入现有连接。
*   **丢包检测和流重组**：为了避免中间盒（middlebox）引起的问题，MPTCP将TCP序列号的作用分离。TCP头中的序列号和累计确认是**按子流**的，用于高效的丢包检测和快速重传。此外，还添加了一个**额外的数据序列号**，指示负载在应用数据流中的位置，以实现可靠的流重组。
*   **流控制**：接收方维护一个**单一的共享缓冲区**，所有子流都根据数据序列空间中最后连续接收的数据来报告接收窗口。这避免了为每个子流维护单独缓冲区可能导致的死锁问题。
*   **数据确认**：为了避免潜在的死锁，数据确认字段通过**TCP选项**传输，而不是作为数据流的一部分。

## BBR

### 为什么基于丢包(loss-based)的拥塞控制算法会导致大延迟和低吞吐？
在上世纪80年代，传统TCP拥塞控制算法将丢包作为发生拥塞的信号。这在那个年代是正确的，而当网卡的速率从Mbps级别进化为Gbps级别，内存从KB级别进化到GB级别之后，丢包与拥塞的关系变得不那么紧密了。

当瓶颈链路的缓存较大时，这些基于丢包的拥塞控制算法将填满缓存，造成了缓冲区膨胀，队列增长，导致较大的延迟；当瓶颈链路的缓存较小时，这些算法会又将丢包作为发生拥塞的信号，从而降低速率导致了较低的吞吐量。

另一种理解：因为基于丢包探测的算法总会使inflight的数据量达到BDP+BtlBufSize这个状态，在现代的路由器中由于缓存很大，相当于把物理链路人为的拉长了，使数据传输的时延变大，即RTT变大。

### 理解BBR给出的原理图
![alt text](image-9.png)

BDP = RTprop × BtlBw，其实际含义是总的发送了，但还没有确认的数据（data in flight），RTprop(Round-Trip propagation time)：往返传输时间，BtlBw(Bottleneck Bandwidth)：瓶颈带宽（传输最慢的链路带宽）。理解：将网络路径看作物理管道的话，则RTprop即为管道长度，而BtlBW是管道最窄处的直径，BDP就可看作是管道的容量（瓶颈容量）。

在BDP点左侧区间，客户端发送数据没有填满发送管道，称为应用受限(app limited)区域。（也可以认为是数据包发送速率没有达到瓶颈带宽BtlBw）。

在BDP点和BDP+BtlneckBufSize点的中间区间，发送数据达到链路瓶颈容量，但还没有超过瓶颈容量+缓冲区容量。应用发送数据主要受带宽限制，因此称为带宽受限(bandwidth limited)区域。

在BDP+BtlneckBufSize点的右侧区间，发送数据超过瓶颈容量+缓冲区容量，多出来的数据会被丢弃。缓冲区的大小决定了丢包的多少，因此称为缓冲区受限(buffer limited)区域。

从图中可以看出，传统基于丢包的拥塞控制算法是基于BDP+BtlneckBufSize点的。当缓冲区被填满，发生丢包时，基于丢包的拥塞控制算法才会开展行动，缓解拥塞。而实际上，数据包已经在缓冲区排队了，同时，现在的缓冲区存储已经比链路BDP还要大了，这种使得“丢包”和“拥塞”并不匹配。这种缓冲区队列的增长将导致秒级的延迟。

本文的BBR算法即针对BDP点去进行拥塞控制，使得缓冲队列始终不会过长，以降低延迟并增大吞吐。

### BBR拥塞控制算法

*   **BBR 算法的两个主要部分**
    *   **接收 ACK 时**：每次收到 ACK 都提供新的 RTT 和交付速率测量，用于更新 RTprop 和 BtlBw 的估计值。BBR 会根据样本是否受应用程序限制（app_limited）来决定是否将其纳入带宽模型，以确保反映的是网络而非应用程序的限制。RTprop(估计)=一段时间内的最小RTT，BtlBw(估计)=传输速率在一段时间窗口内的最大值
    *   **发送数据时**：BBR **对每个数据包进行流量整形（pacing）**，以使其到达速率与瓶颈链路的出站速率匹配。`pacing_rate` 是 BBR 的主要控制参数。次要参数 `cwnd_gain` 将在途数据量限制为 BDP 的少量倍数，以处理常见的网络和接收端病态。

*   **BBR 的状态机和行为**
    BBR 使用一个简单的顺序探测状态机，**在探测更高带宽和探测更低往返时间之间交替进行**。它的行为通过一系列“状态”来定义，这些状态由固定的增益和退出条件组成。
    *   **Startup (启动)**：
        *   在连接开始时使用。
        *   通过将发送速率加倍，实现对 BtlBw 的二分查找。这可以非常快速地发现 BtlBw，但会在搜索的最后一步产生**高达 2BDP 的过量队列**。
        *   在 Startup 状态下，`pacing_gain`(相对发送速率) 和 `cwnd_gain` 设置为 2/ln2，这是允许发送速率每轮加倍的最小值。
        *   当 BtlBw 估计值出现平台期（例如，连续三轮尝试将交付速率加倍但实际增加很少，低于 25%）时，BBR 认为已达到 BtlBw 并退出 Startup，进入 Drain 状态。
        *   与 CUBIC 相比，BBR 在发现可用带宽方面更鲁棒，因为它不会因为丢包或延迟增加而退出搜索。BBR 平滑地加速发送速率，而 CUBIC（即使有 pacing）在每轮中都会发送一连串数据包，然后施加一段静默间隙。
    *   **Drain (排空)**：
        *   在 Startup 发现 BtlBw 后，BBR 转换为 Drain 状态，使用 Startup 状态增益的倒数ln2/2来**消除过量的队列**。
        *   当在途数据包数量与估计的 BDP 相匹配时（即队列已完全排空但管道仍满），BBR 离开 Drain 状态并进入 ProbeBW 状态。
    *   **ProbeBW (探测带宽)**：
        *   BBR 流程**大部分时间（约 98%）都处于 ProbeBW 状态**，这是**稳态行为**。
        *   通过**增益循环 (gain cycling)** 来探测带宽，使用八阶段循环的 `pacing_gain` 值：5/4, 3/4, 1, 1, 1, 1, 1, 1。
        *   这种设计允许增益循环首先以大于 1.0 的 `pacing_gain` 探测更多带宽（如果瓶颈带宽增加，那么发送速率增加和更大的BtlBW采样就会被测量并更新），然后以小于 1.0 的 `pacing_gain` 排空由此产生的任何队列，最后以 1.0 的 `pacing_gain` 巡航并保持一个小队列。
        *   平均增益为 1.0，以**保持高利用率并维持一个小的、有界限的队列**。
        *   为了改善混合和公平性，以及在多个 BBR 流共享瓶颈时减少队列，BBR 会随机化 ProbeBW 增益循环的初始阶段。
    *   **ProbeRTT (探测往返时间)**：
        *   如果 RTprop 估计值在许多秒内未更新（即未测量到更低的 RTT），BBR 会进入 ProbeRTT 状态。
        *   在此状态下，BBR 将在途数据量减少到**四个数据包**，并至少维持一个往返时间，然后返回到先前的状态（Startup(未测量到更低的 RTT) 或 ProbeBW）。
        *   大型流进入 ProbeRTT 会从队列中排出大量数据包，使其他流也能看到新的 RTprop，从而**促使所有流同步地进入 ProbeRTT 状态，使得总队列下降幅度更大**，从而让更多流看到新的 RTprop。
        *   这种**分布式协调是公平性和稳定性的关键**。

## PIFO
PIFO 队列，全称为 **Push-In First-Out 队列**，是一种专门为可编程数据包调度器设计的优先级队列抽象。它在现代交换机硬件中实现灵活的数据包调度算法方面起着核心作用。

以下是关于 PIFO 队列的详细解释：

*   **基本定义与功能**
    *   **本质上是优先级队列**：PIFO 队列是一种特殊的优先级队列，它保持着数据包的调度顺序或调度时间。
    *   **入队机制**：它允许元素根据其“**秩**”（rank）被推入队列中的对应位置。这个秩可以是调度顺序或时间。
    *   **出队机制**：无论元素被推入何处，PIFO 队列总是从队列的**头部**取出元素。秩较低的元素会首先出队；如果两个元素具有相同的秩值，则先入队的元素会先出队。
    *   **秩的计算**：元素的秩在它被排入 PIFO 队列之前进行计算。这个计算被建模为一个“数据包事务”（packet transaction），这是一个原子执行的代码块，在每个元素入队之前执行一次。

*   **PIFO 队列的例子**
    Two flows, A and B, wA=0.8 and wB=0.2, both flows have many packets arriving to the switch, no restriction on queue length, assume no dequeues and packet size is 1
    For flow A, a0.rank=0, a1.rank=1.25, a2.rank=2.5, a3.rank=3.75, a4.rank=5, a5.rank=6.25, …
    For flow B, b0.rank=0, b1.rank=5, b2.rank=10, …
    The PIFO dequeue sequence is: a, b, a, a, a, a, b, a, a, a, a, b, …

    这里权重计算方式为其虚拟时间，即 当前数据包的排名 = 上一个同流数据包的排名 + 1 / 权重

## SP-PIFO
SP-PIFO (Strict-Priority PIFO) 是一种 **近似实现 PIFO 行为** 的方法，旨在克服在高速网络硬件中实现完美 PIFO 队列的巨大挑战。

### 为什么需要 SP-PIFO？

* **理想 PIFO 的挑战：** 理想的 PIFO 队列需要能够将数据包精确地插入到其在排序序列中的正确位置，并始终能以线速（line rate）取出排名最高的数据包。在极高的数据吞吐量下（例如 100Gbps 或更高），维护一个实时、完美排序的队列需要大量的内存操作和复杂的硬件逻辑，这在成本和功耗上都难以承受。
* **现有硬件的限制：** 现有的网络交换机通常基于严格优先级队列（Strict Priority Queues）或循环队列（Round-Robin Queues）来构建调度器，它们不支持在队列中间进行任意位置的插入。

SP-PIFO 正是为了解决这些问题而提出的。它利用现有的硬件结构（严格优先级队列），通过巧妙的映射和动态调整来近似 PIFO 的行为。

### SP-PIFO 的核心原理

SP-PIFO 的核心思想是使用一组 **严格优先级队列 (Strict-Priority Queues)** 来模拟单个 PIFO 队列。它不追求完美的排序，而是通过以下机制来近似实现 PIFO 的“推入任意位置，取出固定头部”的调度行为：

1.  **多级严格优先级队列 (Multi-level Strict-Priority Queues)：** SP-PIFO 将数据包的整个排名范围（rank space）划分为多个区间，每个区间对应一个严格优先级队列。例如，如果一个数据包的排名落在区间 $[X, Y)$，它就会被放入对应于这个区间的严格优先级队列中。

2.  **动态映射 (Dynamic Mapping)：**
    * **队列绑定 (Queue Bounds)：** 每个严格优先级队列都有一个相关的**排名范围**（或称之为“队列绑定”）。当一个数据包到达时，其排名会被用来确定它应该被放置到哪个严格优先级队列中。
    * **出队行为：** 当需要出队时，SP-PIFO 总是从**优先级最高的非空严格优先级队列**中取出数据包（严格优先级队列本身是 FIFO）。
    * **边界调整 (Boundary Adaptation)：** 这是 SP-PIFO 最关键的部分。为了更好地近似 PIFO 的行为，SP-PIFO 会**动态地调整**这些严格优先级队列的排名边界。

3.  **边界调整 (Boundary Adaptation)：**
    SP-PIFO 通过两种动态调整机制——“推上”（Push-up）和“推下”（Push-down）阶段，来动态适应数据平面中的队列，以近似理想的Push-In First-Out (PIFO) 队列行为。其核心思想是动态调整数据包等级与可用严格优先级队列之间的映射关系，从而最大限度地减少调度错误（即“非PIFO行为”或“倒序”），而无需预先了解流量信息。

    以下是这两个阶段如何动态调整队列的详细描述：
    ![alt text](image-10.png)
    ![alt text](image-11.png)

    *   **“推上”阶段 (Push-up)**:
        *   **工作机制**: 当SP-PIFO接收到传入数据包时，它会**从低优先级队列（队列n）向高优先级队列（队列1）自下而上地扫描队列边界**。它会将数据包排入**第一个**满足以下条件的队列 `i`：数据包的等级 `r` 大于或等于该队列的边界 `qi` (即 `r ≥ qi`)，或者该队列是最高优先级队列 (即 `i = 1`)。一旦确定了队列，SP-PIFO会**将该队列的队列边界 `qi` 更新为当前入队数据包的等级 `r`**。
        *   **效果**: 这种映射过程确保了**非最高优先级队列**内部的数据包分配是“零成本”的（即**没有导致倒序**），但最高优先级队列中，即使数据包等级 `r(p)` 小于队列边界 `q1`，数据包仍会被排入，这可能导致倒序的发生。

    *   **“推下”阶段 (Push-down)**:
        *   **目的**: 该阶段旨在抵消“推上”阶段可能在最高优先级队列中导致的倒序效应。它通过减少最高优先级队列中排队的数据包数量来实现这一目标。
        *   **工作机制**: **当SP-PIFO在最高优先级队列中检测到倒序时**（即，传入数据包的等级 `r` 小于最高优先级队列的边界 `q1`）。它会根据倒序的严重程度**减少所有队列的队列边界**。具体来说，SP-PIFO会将所有队列的边界 `qj` 都减少相同的量：`q1 - r(p)`，其中 `r(p)` 是导致倒序的数据包等级。倒序越大，推下得越多。
        *   **效果**: 通过这样做，SP-PIFO确保了未来的高等级数据包将被推到更低优先级的队列中，从而防止它们在最高优先级队列中造成倒序。这种减少队列边界的选择是实用的（因为可以在硬件中高效实现）并且功能强大，因为它在最高优先级队列中的倒序与其他队列中的等级移动之间实现了合理的平衡。


## PCQ

**PCQ 的基本概念**
*   **抽象和实现**：PCQ 是一种将日历队列（Calendar Queue）调度机制与可编程数据包处理管线相结合的抽象。日历队列最初用于离散事件模拟中管理待处理事件集，它是一种优先级队列实现，对于某些优先级分布具有较低的插入和删除成本。
*   **工作原理**：日历队列类似于用于存储未来一年事件的桌面日历，以有序的方式存储事件。它由一系列桶或队列组成，每个桶存储特定日期（或时间段）的事件并按排序顺序排列。事件可以通过将其插入到对应于未来日期的桶中来进行调度。在任何时间点，事件都从“当前日期”的桶中出队并处理。当当前日期的所有事件处理完毕后，就停止处理并进入下一天。空的桶可以重复用于存储未来一年需要执行的任务。
*   **动态优先级提升**：PCQ 允许事件（或数据包）以对应于未来时间的优先级或“等级”入队，并且这个等级会随着时间的推移而逐渐改变。当日历队列从一个“日期”轮换到另一个“日期”时，它会隐式地、批量地提升已缓冲数据包的优先级。这种轮换机制使得调度算法能够随着时间推移提升缓冲数据包的优先级（例如最早截止时间优先 EDF 和公平排队 WFQ），并重用已清空的桶来处理优先级较低的传入数据包。

### WFQ
1.  **数据包状态（Packet State）**和**交换机状态（Switch State）**：
    *   **Packet State**: `weight`：数据包流的权重。
    *   **Switch State**:
        *   `bytes[f]`：流 f 已发送的字节数（或已入队字节数）。
        *   `round`：当前轮次号。
        *   `BpR`：每轮发送的字节数（Bytes per Round）。

2.  **等级计算与入队（Rank Computation & Enqueueing）**：
    *   **`bytes[f] = max(bytes[f], round * BpR * weight)`**：更新流 f 的已发送字节数，确保其不小于基于当前轮次和权重计算出的值。
    *   **`n = (bytes[f] + pkt.size) / (BpR * weight) - round`**：计算数据包应该被调度到未来多少个周期（`n`）后。
    *   **`CQ.enqueue(n)`**：将数据包入队到由 `(currQ + n) % N` 决定的相应队列中。

3.  **队列轮换（Queue Rotation）**：
    *   **`if CQ.dequeue() is null`**：如果当前队列（头队列）为空，则触发轮换。
    *   **`CQ.rotate()`**：执行日历队列的轮换操作。
    *   **`round = round + 1`**：当前轮次号增加。

### 示例调度步骤详解

**给定参数**：
*   Flow 1: weight = 0.3, packets = 3
*   Flow 2: weight = 0.4, packets = 4
*   Flow 3: weight = 0.3, packets = 3
*   BpR (Bytes per Round) = 4
*   队列数量 (N) = 2 (假设物理队列索引为 0 和 1)
*   时间 0 时，所有数据包同时到达。
*   为简化计算，假设每个数据包的大小 `pkt.size` 为 **1 字节**。
*   初始状态：`round = 0`，`currQ = 0` (头队列是队列 0)，所有 `bytes[f]` 计数器初始化为 0。

**步骤 1：数据包入队计算**
我们假设数据包按照 Flow 1, Flow 2, Flow 3 的顺序，并在每个Flow内部按顺序进行处理，然后入队。

*   **Flow 1 (weight = 0.3)**
    *   **F1P1 (Flow 1, Packet 1)**: `pkt.size = 1`
        *   `bytes[F1] = max(0, 0 * 4 * 0.3) = 0`
        *   `V_finish = (0 + 1) / (4 * 0.3) = 1 / 1.2 ≈ 0.833`
        *   `n = floor(0.833) - 0 = 0`
        *   **入队队列**：`(currQ + n) % N = (0 + 0) % 2 = 0`。
        *   `bytes[F1]` 更新为 `0 + 1 = 1`。
    *   **F1P2 (Flow 1, Packet 2)**: `pkt.size = 1`
        *   `bytes[F1] = max(1, 0 * 4 * 0.3) = 1`
        *   `V_finish = (1 + 1) / (4 * 0.3) = 2 / 1.2 ≈ 1.667`
        *   `n = floor(1.667) - 0 = 1`
        *   **入队队列**：`(0 + 1) % 2 = 1`。
        *   `bytes[F1]` 更新为 `1 + 1 = 2`。
    *   **F1P3 (Flow 1, Packet 3)**: `pkt.size = 1`
        *   `bytes[F1] = max(2, 0 * 4 * 0.3) = 2`
        *   `V_finish = (2 + 1) / (4 * 0.3) = 3 / 1.2 = 2.5`
        *   `n = floor(2.5) - 0 = 2`
        *   **入队队列**：`(0 + 2) % 2 = 0`（丢弃）。
        *   `bytes[F1]` 更新为 `2 + 1 = 3`。

Flow 2, Flow 3 同理，入队后一轮发0队，二轮发1队

**PCQ 的特点与优势**
*   **硬件实现友好**：日历队列具有某些特性，使其适用于高效的硬件实现，尤其是在即将出现的可编程交换机硬件上。它包含多个物理交换机队列，并且在任何时间点，只有一个队列处于活动状态。此外，日历队列强制规定了激活队列的固定轮换顺序。
*   **支持多种调度策略**：通过与可编程交换机管线（如 Barefoot Tofino 和 Cavium Xpliant）结合，PCQ 可以模拟各种调度策略。这些包括 LSTF 变体（提供更强的延迟保证）、公平排队（提供对突发友好的公平性）和 pFabric 变体（提供无饥饿的短流优先级）。
*   **灵活的定制化**：可编程交换机上的可编程数据包处理管线允许定制等级计算以及日历队列的轮换过程。
    *   **未来调度**：调度算法必须决定传入数据包应在多远的未来进行调度（即，选择未来周期 [0, N-1] 将数据包入队）。这类似于 PIFO 中的等级计算。
    *   **时间推进**：调度算法必须定期决定何时以及如何推进时间（即，决定何时一个周期结束并进入下一个周期）。这会停止当前周期的数据包入队，并允许将相应的队列资源重新用于未来 N 个周期的数据包。
    *   **管线状态修改**：当 CQ 进入下一个周期时，需要相应修改管线状态，以确保正确计算传入数据包的等级。

**局限性**
*   **队列内反转**：PCQ 中单个桶（FIFO 队列）内的事件不一定按严格排序，这可能导致同一桶内优先级较高的包在优先级较低的包之后调度，引入近似。
*   **优先级范围限制**：PCQ 对其可以同时入队的等级范围有上限。如果调度算法需要很大的范围，则无法精确实现。
*   **分层日历队列（Hierarchical Calendar Queues，HCQs）**：可以通过分层结构扩展 CQ 的范围。例如，一个 2 级 HCQ 可以将 N 个 FIFO 队列分成两组，分别运行独立的日历队列，但具有不同的桶间隔，从而提供更大的总时间周期范围。但这会增加数据包循环的成本和额外状态管理。
*   **无法改变已缓冲包的相对顺序**：PCQ 只能在数据包到达时计算入队等级，无法改变已缓冲数据包的相对顺序。这使得它无法实现某些机制，例如 pFabric 的饥饿预防技术，该技术要求后来收到的数据包改变先前入队数据包的优先级。
*   **无法正确排序过去的等级**：PCQ 只能调度未来的数据包。如果计算出的等级在当前 CQ 时间之前，则调度结果将不同于所需的顺序。

## AIFO

AIFO（Admission-In First-Out）队列是一种**新型的可编程数据包调度解决方案**，它**仅使用一个先进先出（FIFO）队列**。

以下是关于AIFO队列的详细讨论：

*   **动机与核心思想**
    AIFO的核心思想是**通过维护一个滑动窗口来跟踪最近数据包的等级（ranks），并计算到达数据包在窗口中的相对等级，以进行准入控制**。这意味着，即使队列仍有空间，AIFO也可能根据数据包的相对等级主动丢弃它。

*   **与现有方案的对比**
    *   **PIFO (Push-In First-Out)**：**PIFO在实践中实现起来具有挑战性**，尤其是在线速（line rate）下支持排序队列。它的设计还存在可扩展性限制，只能支持数千个流。
    *   **SP-PIFO**：**SP-PIFO需要多个珍贵的严格优先级队列**，这在现代数据中心中是稀缺资源，通常用于确保多租户应用之间的物理隔离。
    *   **AIFO的优势**：与PIFO和SP-PIFO不同，**AIFO可以以线速在现有硬件上运行，并且只需最少的交换机资源——仅一个FIFO队列**。这使得AIFO在实际部署中具有重要的优势，允许网络运营商将物理队列保留用于强物理隔离和租户间区分，同时使用AIFO来编程租户内流量的数据包调度算法。

*   **AIFO算法**
    * **时间维度（Temporal Component）**
        时间维度关注的是**队列的整体动态**，其目的是**动态调整准入阈值以匹配到达速率和离开速率**。
        *   **动态阈值**：准入控制的阈值不是固定的，而是根据到达速率和离开速率之间的实时差异进行更新。
        *   **队列长度与目标队列大小**：算法通过比较**当前队列长度（`c`）**和**目标队列大小（`C`）**来捕获这种差异。
            *   `C`：AIFO的目标队列大小，不一定是物理队列的实际最大容量，通常配置为一个较小的值以实现低延迟。
        *   **阈值计算**：当当前队列长度`c`接近目标队列大小`C`时，准入阈值会变得更加严格（即` (C-c)/C `的值变小）。
        *   **预留空间（Headroom）**：AIFO引入了一个参数`k`来分配一个预留空间（`k * C`），以容忍小突发流量。当队列长度`c`小于或等于这个预留空间时（`c <= k * C`），所有数据包都会被无条件准入。这简化了算法的判断，因为当`c <= k * C`时，其空间阈值计算结果 `1 / (1-k) * (C-c)/C` 必然大于等于1，而数据包的百分位数（quantile）最大为1。
        *   **确保速率匹配**：这种动态调整确保了被准入数据包的速率大致与离开速率匹配，防止队列过度膨胀。

    * **空间维度（Spatial Component）**
        空间维度关注的是**数据包的等级（rank）分布**，其目的是**根据数据包的相对等级来决定丢弃哪个数据包**。
        *   **等级感知丢弃**：AIFO倾向于**丢弃高等级（较低优先级）的数据包，而保留低等级（较高优先级）的数据包**，因为低等级数据包预计会首先被PIFO调度。
        *   **滑动窗口**：为了实现等级感知，AIFO维护一个**滑动窗口（sliding window）**来跟踪最近收到的数据包的等级。
        *   **百分位数估计**：对于每一个到达的数据包，AIFO会估计其在滑动窗口中等级的**百分位数（quantile）**，这代表了其在最近数据包等级分布中的相对位置。
        *   **准入判断**：
            *   如果数据包的等级百分位数**不大于**由时间维度计算出的阈值（即`W.quantile(pkt) <= 1 / (1-k) * (C-c)/C`），则数据包被准入。
            *   否则，数据包被丢弃，即使队列可能仍有空间。
![alt text](image-12.png)

*   **特性与优点**
    *   **单FIFO队列**：AIFO只需要一个简单的FIFO队列，这使其在现有交换机上易于实现和部署。
    *   **防止饥饿和数据包乱序**：作为一个意外的积极副作用，AIFO**通过设计自然地支持饥饿预防**，这是一个pFabric（用于最小化FCT）所需的特性，可以防止数据包乱序。PIFO在实现SRPT时会导致数据包乱序，因为后来的数据包可能因为剩余处理时间更短而被优先调度。而AIFO由于仅在FIFO队列上执行准入控制，因此不会导致数据包乱序。
    *   **动态适应性**：AIFO的准入控制阈值可以根据当前的流量负载动态调整，从而能够很好地处理不同大小的流量。
    *   **低FCT**：在模拟中，AIFO在最小化FCT方面表现出色，与PIFO和SP-PIFO等最先进的方法相比，实现了显著更低的FCT，尤其是在高负载情况下。

*   **数据平面设计与实现**
    *   **队列长度估计**：由于队列长度信息通常只能在交换机出队（egress pipe）时获得，而AIFO需要在入队（ingress pipe）时使用它，因此设计了一种**基于再循环（recirculation）的解决方案**。工作数据包（worker packets）在出队时读取队列长度信息，然后被再循环回入队，更新入队端的队列长度信息。
    *   **百分位数估计**：AIFO使用一系列阶段来实现滑动窗口，存储最近接收到的数据包并估计百分位数。通过一个索引标记模块（index tagger）和比较到达数据包等级与窗口中存储的等级，可以计算出数据包的相对等级百分位数。为了高效利用资源，AIFO还支持**采样方法**来虚拟地扩大滑动窗口大小。

## Cebinae

### 1. 最大最小公平性（Max-min Fairness）和注水算法（Water-filling Algorithm）

#### 1.1 最大最小公平性（Max-min Fairness）
最大最小公平性是网络公平性的**一个经典目标**，通常被认为是拥塞控制算法应实现的一个**关键特性**。

*   **定义**: 一个速率分配集合 𝑅 = {𝑟1, . . . , 𝑟𝑛} 被称为“最大最小公平”的，当且仅当对于任何其他分配 𝑠 ∈ 𝑅 和所有流 𝑖，如果 𝑠𝑖 > 𝑟𝑖，则必然存在一个流 𝑗，使得 (𝑟𝑗 ≤ 𝑟𝑖 且 𝑠𝑗 < 𝑟𝑗)。换句话说，在另一个分配中，存在一个较小的流 𝑟𝑗，其容量会减少。
*   **另一个定义**: 对全局中任意流𝑟𝑖，存在链路l，l是饱和的且𝑟𝑖的流量在l链路的所有流中最大（l是𝑟𝑖的瓶颈链路）。
*   **特性**: 最大最小公平的流分配在常见网络假设下是**可证明的帕累托效率且是唯一的**。帕累托效率意味着增加任何流的速率都必然导致另一个流的速率减少。

#### 1.2 注水算法（Water-filling Algorithm）
注水算法是计算最大最小公平分配的**最常用方法**。

*   **直观解释**: 该算法直观地工作方式是，**首先将所有流初始化为“无约束”且速率为0**。在每次迭代中，算法**平等地增加所有无约束流的速率**，直到网络中至少有一条链路达到饱和。所有经过饱和链路的流现在被认为是“受约束”的。算法会持续迭代，直到所有流都被约束。
*   **与传统TCP拥塞控制的关系**:
    *   **相似之处**: 传统的TCP拥塞避免与注水算法有许多相似之处。在理想情况下，所有发送方的速率会以每个RTT一个MSS的速度增加，直到它们满足需求或检测到拥塞并变为“受约束”。
    *   **差异之处**: 然而，现实中TCP与理想的注水算法在多个方面存在差异。流并非同时以零速率初始化；速率增量不是同步的，也不是均匀的（取决于RTTs）；实际应用的需求并非无限；发送通常基于未确认字节而非固定速率；并且连接可以具有异构的拥塞检测方法（如丢包、延迟、ECN、混合等）以及增加/减少算法。这些差异可以解释现代互联网中许多不公平的问题。
*   **公平队列（Fair Queuing）**: 公平队列机制在每个输出队列上更精确地实现了注水框架。它（理想地）以逐比特轮询的方式为每个流分配单比特容量，直到它满足所有流的需求或使链路容量饱和。

### 2. Cebinae如何实现最大最小公平性

Cebinae通过**对超出其最大最小公平份额的流量施加惩罚**，从而增强网络公平性。它旨在将带宽从已达到或超过其公平份额的流量**重新分配**给尚未达到公平份额的流量。

以下是Cebinae实现最大最小公平性的具体方法和关键特性：

*   **[重要]带宽再分配机制——“税收”方法**:
    *   假人：在每个饱和链路上，找到最大大小的重度流量，对其施加令牌桶速率限制。当总需求低于链路容量时，限制将被解除。
    *   Cebinae不同于简单的“假人”方案（即仅阻止超额流量但不主动重新分配），它**不会冻结瓶颈流的速率**，而是**尝试重新分配这些流的一小部分带宽**。这相当于对每条链路上最大流量的“征税”。
    *   虽然这种“税收”会给网络的最坏情况吞吐量带来一些开销，但它确保了占用超过其公平份额的流量**无法永远主导网络**。税率 𝜏 是一个可配置的参数，允许在收敛速度和开销之间进行权衡。
    *   为了促进稳定性和摊销税收，Cebinae将税收应用于**最大速率的流以及与最大速率相差在 𝛿 范围内的任何流**，其中 𝛿 是另一个可配置参数，代表允许的不公平程度。
*   **支持现代拥塞控制协议**:
    *   为了更好地服务于现代拥塞控制需求，Cebinae将**延迟和ECN**作为额外的拥塞信号引入，这些信号在丢包之前触发。它通过一个修改和近似的漏桶过滤器来实现这一点。
*   **流量分类与检测**:
    *   Cebinae通过**本地信息**（聚合字节计数器和重度使用者流量大小跟踪）有效地验证网络的最大最小公平性。
    *   它将流量分为两大类：**瓶颈流 (bottlenecked, ⊤)** 和**非瓶颈流 (unbottlenecked, ⊥)**。
    *   当端口饱和时，Cebinae会检测哪些流量是当前链路的瓶颈，这通常是链路上观察到的**最大速率的流**。
    *   出口管道流速缓存，用于精细跟踪端口饱和度和瓶颈流状态。跟踪出口管道的利用率，并为每个端口维护一个简单的传输字节计数器。如果利用率高于 (1 − 𝛿𝑝)·容量，则认为该链路已饱和。使用多级哈希表检测瓶颈流。查找任何流的最大字节计数器 𝑐max，如果其字节计数 𝑐i 满足 𝑐i ≥ 𝑐max · (1 − 𝛿𝑓)，则将流声明为“瓶颈”。
    *   入口管道流调度器，用于向瓶颈流注入延迟/丢包，以限制和重新分配其带宽。仅跟踪两个流组——⊤⊥。使用仅包含两个队列/优先级（headq 和 ¬headq）的日历队列。对于每个数据包，它会获取目标流组的速率分配（⊤/⊥），并计算数据包的预期发送时间。速率是权重。将此发送时间转换为时间段和关联的物理队列。
    *   低延迟控制平面代理，用于记录流速并动态调整瓶颈流成员资格和发送速率限制。
*   **资源效率与可扩展性**:
    *   Cebinae只需要**两个队列/优先级**来对流量进行分组，而不是为每个流分配独立的队列。这最大化了可用缓冲区，并最小化了对队列和优先级层次的需求。
    *   与AFQ、PCQ或理想FQ不同，Cebinae所需的物理队列数量**不随流量数量增加而改变**。
    *   它通过减少流跟踪的粒度（从所有流到仅两个流组）来**节省SRAM**，消除了哈希冲突导致不公平的可能性，并显著降低了全利用率下的𝐵𝑝𝑅要求。
    *   Cebinae的计算和内存资源消耗**不到25%**，例如在32端口Tofino交换机上。
    *   Cebinae具有可配置参数，包括**端口饱和度阈值 (𝛿𝑝)**、**流量瓶颈阈值 (𝛿𝑓)** 和**税率 (𝜏)**。
    *   其他参数如重新计算周期(P)、控制平面截止时间(L)、虚拟桶持续时间(vdT)和物理桶持续时间(dT)则根据网络特性进行设置.


## FlowRadar

*   **目的和优势**：NetFlow是广泛使用的监控工具，但其在数据中心交换机上的硬件实现面临挑战，因为其对每包处理时间、内存和带宽都有严格限制，导致许多现有解决方案不得不采用采样方式。FlowRadar的提出是为了**满足在短时间尺度内无需采样即可监控所有流量的需求**。它能以小内存和恒定插入时间扩展到大量流，并帮助运营商更好地洞察其网络。
*   **核心思想**：FlowRadar的关键思想是**在交换机端以小内存和恒定插入时间对每流计数器进行编码**，然后**利用远程收集器的强大计算能力执行全网络范围的解码和流计数器分析**。这种设计识别了廉价交换机（每包处理时间有限）和远程收集器（计算资源充足）之间的最佳分工。

### 1. Bloom Filter
Bloom filter是一种数据结构，包含一个m比特向量和k个哈希函数$h_i$(值域为[0,m-1])，它能够用来近似地判断某个元素是否在某个集合中。

*   **插入元素**: 将元素x插入到Bloom filter中，需要执行k次哈希操作，将哈希值对应的比特位置为1。
*   **查询元素**: 查询元素x是否在Bloom filter中，需要执行k次哈希操作，如果所有哈希值对应的比特位都为1，则认为x在集合中，否则认为x不在集合中。

随着存入元素的增多，即便一个元素并不存在于对应集合中，但其经过Hash映射的对应bit位却被其他元素的Hash映射置为1，就会导致误判。当已经插入的元素个数为n时，特定bit位仍然为0的概率为：$( 1-\frac{1}{m} )^{kn} \approx e^{-kn/m}=f $，对于一个不存在的元素，其可能被判断存在集合中的概率：$(1-f)^k=(1-e^{-kn/m})^k$。为了最小化False Positive的概率，可以得到最优的哈希函数个数：$k=\frac{m}{n}ln2$。

**counting Bloom filter**: 比特向量变为m位正整数向量，插入元素时，将$h_i(x)$对应的比特位加1，删除元素时，将$h_i(x)$对应的比特位减1，查询元素x时，计数为$h_i(x)$比特位的最小值。

### 2. FlowRadar

* **编码流程集数据结构**
  * flow filter：一个由 $m_f$ 位数组和 $k_f$ 个哈希函数组成的布隆过滤器
  * counting table：存储流计数器，一个 $m_c$ 长和 $k_c$ 个哈希函数的扩展布隆过滤器，每个单元包含三个字段
      * FlowXOR：映射到此单元的所有流 ID 的异或
      * FlowCount：映射到此单元的流数量
      * PacketCount：映射到此单元的流数据包数量

* **Packet Processing(Encode)**
    当接收到一个包时，首先用flow filter检查这个流是否已经存在中。如果是一个新流，就更新计数表(counting table)，异或flow ID和FlowXOR字段，增加FlowCount和PacketCount；如果是一个已经存在的流，只增加PacketCount。
    ![alt text](image-13.png)
* **Single Decode**
    **首先**检查**FlowCount=1**的单元，进而就可以找到这个单元里Flow对应的ID和包数，这个ID的Flow就得到了解码。下面对这个解码Flow执行Flow filter的哈希函数，去**定位**跟它相关的其他Flow fliter单元，对这些单元对应的Counting table的FlowXOR字段执行异或操作(**从其他单元中去除该流的影响**)，FlowCount和PacketCount都减去解码Flow的对应数字，得到Single Decode解码出来的单个Flow和剩下Flowset。这样就认为是做了Single Decode，其主要是先解码单个Flow。

* **Decode**
    FlowDecode&CounterDecode(Network-wide Decode)：利用网络上多个交换机的信息，每个交换机都会有一个Flow filter和对应的Counting table。

    FlowDecode：对于任意两个邻居的Single Decode，检查一方Single Decode解码的所有单个Flow是否在另一方的Flowset中，如果存在，就将这些单个Flow从另一方的Flowset中删去（包含FlowXOR等字段）。对两者都进行检查之后，都分别调用Single Decode，去看能否从这两个邻居的Flowset中解码更多信息。

    CounterDecode：虽然可以使用FlowDecode轻松解码流，但这不能解码它们的计数器。这是因为由于丢包和动态包的原因，同一流在A和B处的计数器可能不相同。但从FlowDecode的过程中，我们可能已经知道一个编码Flowset中的所有流。也就是说，在每个单元中，我们知道单元中的所有流以及这些流计数器的汇总。假设流集中有mc个单元和n个流，总共有mc个方程和n个变量。这意味着需要解MX = b，其中X是n个变量的向量，M和b由上述方程构建。

    ![alt text](image-14.png)

## Elastic Sketch

Elastic Sketch 是一种新颖的草图算法，旨在解决当前网络中流量特性（如可用带宽、数据包速率和流大小分布）急剧变化时，网络测量性能显著下降的问题。Elastic Sketch 具有以下主要特点：

*   **自适应性**：它能自适应当前的流量特性，包括带宽、数据包速率和流大小分布的变化。
*   **通用性**：它适用于多种测量任务和平台。
*   **高性能**：与现有最先进的方法相比，Elastic Sketch 在速度和错误率方面均有显著提升。
*   **准确性**：通过分离关键信息并丢弃不必要的信息，它能保持高准确度。

### Elastic Sketch 的数据结构 (Data Structure)

Elastic Sketch 的数据结构由**两部分组成**：

*   **重度部分 (Heavy part)**：主要用于记录 **“大象流”（elephant flows）**，即流量较大的流。
*   **轻度部分 (Light part)**：主要用于记录 **“老鼠流”（mouse flows）**，即流量较小的流。

这种分离技术被称为 **Ostracism（陶片放逐法）**，旨在将大象流和老鼠流精确地分开。

#### 重度部分 (Heavy part)

*   **结构**：重度部分是一个 **哈希表**，每个桶（bucket）记录一个流的信息，并与一个哈希函数 `h(.)` 相关联。
*   **每个桶的字段**：
    *   **流 ID (key)**：用于标识流。
    *   **正向投票 (vote+)**：记录属于该流的数据包数量，即流的大小。
    *   **标志位 (flag)**：指示轻度部分是否可能包含该流的正向投票。
    *   **反向投票 (vote-)**：记录不属于该流（即流 ID 与桶中存储的流 ID 不匹配）的其他数据包的数量。

#### 轻度部分 (Light part)

*   **结构**：轻度部分是一个 **CM Sketch (Count-Min Sketch)**。
*   **组成**：
    *   由 `d` 个数组 (`L1, L2, ..., Ld`) 组成。
    *   每个数组都与一个哈希函数相关联（映射范围为 `[0, w)`）。
    *   每个数组包含 `w` 个计数器（数组的宽度为 `w`）。
*   **优点**：由于不记录老鼠流的 ID，轻度部分可以使用许多小型计数器（例如8位计数器），从而非常精确。

### 如何编码 (How to encode) / 插入 (Insertion)

当一个带有流 ID `f` 的数据包到达时，Elastic Sketch 的插入过程如下：

1.  **哈希**：将流 ID `f` 哈希到重度部分中的一个桶 `H[h(f)%B]`，其中 `B` 是重度部分的桶数量。
2.  **判断并处理**：根据桶的当前状态和 `f` 与桶中已有流的关系，分为四种情况：
    *   **情况 1：桶为空**：直接将 `(f, 1, F, 0)` 插入到该桶中。`F` 表示此桶中尚未发生驱逐（eviction）。
    *   **情况 2：`f` 与桶中已有的流 `f1` 相同**：将 `f1` 的 `vote+` 简单地加 1。
    *   **情况 3：`f` 与 `f1` 不同，且 `vote-` 与 `vote+` 的比值小于预设阈值 `λ` （例如 `λ=8`）**：
        *   将 `f1` 的 `vote-` 加 1。
        *   将 `(f, 1)` 插入到 **CM Sketch（轻度部分）**：对 `f` 计算 `d` 个哈希函数，找到 `d` 个对应的计数器，并将它们的值加 1。
    *   **情况 4：`f` 与 `f1` 不同，且 `vote-` 与 `vote+` 的比值大于等于预设阈值 `λ`**：
        *   “驱逐” `f1` 并“选举” `f`：将该桶设置为 `(f, 1, T, 1)`（论文笔误？应该是`(f, 1, T, 0)`）。
        *   将 `f1` 驱逐到 **CM Sketch（轻度部分）**：根据 `f1` 的 `vote+` 值，将其对应的 `d` 个哈希计数器增加 `vote+` 的数量。
        *   此时，桶中的标志位被设置为 `T` (true)，因为 `f1` 在被驱逐之前可能已经有一些`f`流被计入`vote-`并在此时转移到轻度部分中。

重要的是，插入操作必须是**单向的**，因为在硬件平台上进行回溯操作很困难。

### 如何查询 (How to query)

要查询任何流的流量大小时，Elastic Sketch 的查询过程如下：

*   **对于不在重度部分中的流**：直接从轻度部分（CM Sketch）查询并报告其大小。
*   **对于在重度部分中的流 `f`**：
    *   **情况 1：`f` 的标志位为 `False`**：其大小就是重度部分中对应的 `vote+` 值，**没有误差**。
    *   **情况 2：`f` 的标志位为 `True`**：需要将重度部分中对应的 `vote+` 值与从 CM Sketch 中查询到的结果**相加**，才能得到该流的总大小。

由于大象流和老鼠流的分离，Elastic Sketch 在大多数情况下都能提供高准确度。对于标志位为 `False` 的流，重度部分记录的 `vote+` 值是精确的，没有误差。对于标志位为 `True` 的流，重度部分记录的 `vote+` 值是流大小的一部分，同样没有误差，而另一部分则记录在可能带有误差的轻度部分中。由于轻度部分仅记录老鼠流的大小，并使用大量小型计数器，其本身也能保持高准确度。

![alt text](image-15.png)

## LossRadar
LossRadar是一种专门为数据中心网络设计的轻量级丢包检测系统，旨在快速检测网络中的丢包事件，并提供丢失数据包的详细信息，如发生位置和数据包头部信息，以帮助诊断和减轻这些损失的影响。它通过捕获单个丢失的数据包及其详细信息，实现在精细时间尺度上覆盖整个网络的监控。

以下是关于您的具体问题：

*   **什么是Invertible Bloom Filter (IBF)?**
    Invertible Bloom Filter (IBF)，或称可逆布隆过滤器，是LossRadar**流量摘要**（Traffic Digest）设计的基础。
    *   **结构组成**：IBF摘要由一个**单元数组**组成，每个单元格包含两个值：`xorSum` 和 `count`。
    *   **数据包插入**：当一个数据包 `x` 到达时，系统会将其插入到 `k` 个单元格中。
        *   每个单元格的 `xorSum` 值会更新为 `xorSum` 异或 `x.sig`，其中 `x.sig` 是数据包的**签名**。
        *   每个单元格的 `count` 值会增加1。
        *   这 `k` 个单元格的索引是通过对 `x.sig` 计算 `k` 个哈希函数 (`h1...hk`) 得到的。
    *   **签名内容**：数据包的签名 `x.sig` 包含能够唯一识别数据包的**不可变头部字段**，例如**5元组**（源/目的IP地址、端口和协议）和**IP_ID**。此外，操作员还可以配置包含其他感兴趣的字段，如TCP序列号、TCP标志位或时间戳等，以提供更详细的信息。
    *   **作用**：IBF的设计允许在后续步骤中通过数学运算（减法）来识别和恢复其中包含的元素，这是其“可逆”特性的体现。

*   **如何解码？**
    解码过程指的是从IBF摘要中**恢复单个数据包的签名**。
    *   **识别纯单元格**：首先，在摘要中找到那些包含“正好一个数据包”的单元格，这些单元格被称为**纯单元格**（`count = 1`）。
    *   **提取签名**：纯单元格的 `xorSum` 值正是其中包含的数据包的签名。
    *   **迭代移除**：
        1.  一旦识别出某个数据包的签名，就将该数据包从其哈希到的所有 `k` 个单元格中移除。
        2.  移除操作通过将该数据包的签名与这些单元格的 `xorSum` 进行异或，并将其 `count` 值减1来完成。
        3.  移除后，可能会有新的纯单元格出现，系统会重复上述过程。
    *   **高概率恢复**：通过这种迭代过程，可以以很高的概率检索到摘要中包含的所有数据包。

*   **如何检测和解码丢失的数据包？**
    LossRadar通过部署计量器和利用流量守恒规则来检测并解码丢失的数据包。
    *   **检测机制**：
        1.  **流量守恒规则**：LossRadar的关键思想是基于**流量守恒规则**。该规则指出，进入任何网络域（例如链路、交换机模块、交换机或网络设备区域）的数据包集合应等于离开该域的数据包集合，前提是该域不包含任何数据包源或汇（如中间件、主机）。
        2.  **计量器部署**：LossRadar在网络域的**上游（UM）和下游（DM）**位置部署计量器，以捕获单向流量。
        3.  **流量摘要生成与上报**：每个计量器将单个数据包的唯一标识符编码成一个**流量摘要**，并定期（例如每10毫秒）将这些摘要上报给一个中央LossRadar收集器。
        4.  **摘要比较**：LossRadar收集器会比较上游和下游计量器报告的流量摘要。
        5.  **不匹配即丢包**：**任何摘要之间的不匹配都表明存在丢包**。
        6.  **低开销**：这种方法使得摘要的大小与**丢失数据包的数量成比例**，而不是与所有传输的数据包或流量流的总数成比例，从而显著降低了内存和带宽开销。
    *   **解码丢失数据包**：
        1.  **摘要相减**：为了识别丢失的数据包，收集器会从上游计量器（UM）的摘要中“减去”下游计量器（DM）的相应摘要。
        2.  **抵消原理**：这种减法操作的原理是，在两个摘要中都存在的数据包会相互抵消，**只有在下游摘要中未出现的数据包（即丢失的数据包）会保留在结果摘要中**。
        3.  **计算方式**：具体来说，对于每个对应的单元格 `i`，差值摘要 `L` 的 `xorSum` 为 `UM_i.xorSum XOR DM_i.xorSum`，`count` 为 `UM_i.count - DM_i.count`。
        4.  **恢复丢失数据包**：然后，收集器会使用上述“如何解码”部分描述的**IBF解码过程**，从这个差异摘要 `L` 中恢复每个丢失数据包的标识符和详细信息。
        5.  **详细信息**：LossRadar能够捕获丢失数据包的所有细节，如5元组、发生时间（timing）和序列模式等，这些信息对于诊断丢包的根本原因至关重要。

## On-Off Sketch
On-Off Sketch 是一种为解决数据流中**持久性（persistence）**问题而提出的速准算法，它旨在处理持久性估计和查找持久项这两个典型问题。与传统的频率（frequency）关注项在数据流中出现的总次数不同，持久性强调一个项在许多时间窗口内反复出现的行为。例如，一个人在一年内访问一个网站的天数就是一种持久性，这可以指示该人对网站的偏好。

以下是对 On-Off Sketch 及其工作原理的讨论：

### On-Off Sketch 的两部分 (Two parts of On-Off sketch)

当 On-Off Sketch 用于**查找持久项**时，其数据结构包含两个主要部分：

*   **第一部分：计数器数组**
    *   这部分是一个由 `l` 个计数器组成的数组，记作 `C1[i]`。
    *   它主要用于记录**非持久项**的估计持久性。
    *   此数组与一个哈希函数 `h1(.)` 相关联，将每个项映射到数组中的一个计数器。
*   **第二部分：桶数组（buckets）**
    *   这部分是一个由 `l` 个桶组成的数组，记作 `B[i]`。
    *   每个桶 `B[i]` 都对应于第一部分中的一个计数器 `C1[i]`。
    *   每个桶包含 `w` 个**键值（KV）对**，其中键是项的 ID，值是对应的计数器。
    *   这部分专门用于记录**持久项**的信息。
    *   每个计数器（无论是第一部分还是第二部分中的）都有一个**状态字段**（State field），包含两种状态：**On** 和 **Off**。初始时，所有状态字段都设置为 On，所有计数器都为零。

对于**持久性估计**，On-Off Sketch 的数据结构则相对简单，它由 `d` 个数组组成，每个数组包含 `l` 个计数器。每个计数器也都有一个状态字段（On 或 Off）。

### 如何更新 (How to update)

On-Off Sketch 的更新（插入）操作因其目标（持久性估计或查找持久项）而异：

1.  **持久性估计的插入过程**：
    *   当插入项 `ei` 时，On-Off Sketch 会计算 `d` 个哈希函数，将 `ei` 映射到 `d` 个计数器 `Cj[hj(ei)]`，每个数组中一个。
    *   对于这 `d` 个被映射的计数器，有两种情况：
        *   **情况 1：计数器的状态为 On**。这表示该计数器在当前时间窗口尚未被访问过。On-Off Sketch 会将该计数器加一，并将其状态转为 **Off**。
        *   **情况 2：计数器的状态为 Off**。这表示该计数器在当前时间窗口已被访问过。On-Off Sketch 不会改变计数器。
    *   **周期性清空 (Periodical Emptying)**：在每个时间窗口结束时，On-Off Sketch 会将**所有状态字段设置回 On**。
    *   **关键洞察**：这种设计确保了每个计数器在每个时间窗口内**最多只增加一次**，无论有多少项映射到它。这显著减少了哈希冲突带来的误差，并消除了重复项带来的误差。

2.  **查找持久项的插入过程**：
    *   当插入项 `ei` 时，On-Off Sketch 首先检查 `ei` 是否已经记录在桶 `B[h1(ei)]` 中。
    *   **如果 `ei` 已经记录在桶中**：On-Off Sketch 会增加 `B[h1(ei)][ei]` 中对应的计数器（遵循上述“计数器增长”规则：状态为 On 才增长并转为 Off）。
    *   **如果 `ei` 未记录在桶中**：On-Off Sketch 会增加计数器 `C1[h1(ei)]`（同样遵循“计数器增长”规则）。
    *   **比较与替换**：在上述插入操作之后，On-Off Sketch 会将 `C1[h1(ei)]` 与 `B[h1(ei)]` 中最小的计数器 (`B[h1(ei)]min`) 进行比较，以判断 `ei` 是否足够持久，可以存储在桶中。
        *   **如果 `C1[h1(ei)] > B[h1(ei)]min`**：这表示 `ei` 的估计持久性较大，应该存储在桶 `B[h1(ei)]` 中。因此，On-Off Sketch 会将 `B[h1(ei)]min` 的键设置为 `ei`，并**交换 `C1[h1(ei)]` 和 `B[h1(ei)]min` 的值以及它们的状态**。
        *   **如果 `C1[h1(ei)] <= B[h1(ei)]min`**：这表示 `ei` 不够持久，On-Off Sketch 不会进行任何操作。
    *   **周期性清空 (Periodical Emptying)**：在每个时间窗口结束时，On-Off Sketch 会将**所有状态字段设置回 On**。
    *   **关键洞察**：通过将持久项与非持久项分离，并只记录持久项的 ID，On-Off Sketch 能够保护持久项免受其他项的哈希冲突影响，从而进一步提高准确性。

### 如何查询 (How to query)

查询操作也根据 On-Off Sketch 的目标而有所不同：

1.  **持久性估计的查询过程**：
    *   要查询项 `ei` 的持久性，On-Off Sketch 首先计算 `d` 个哈希函数，以获得 `d` 个映射的计数器。
    *   然后，它会报告这 `d` 个计数器中的**最小值**作为 `ei` 的估计持久性 (`pi = min(Cj[hj(ei)])`)。

2.  **查找持久项的查询过程**：
    *   要报告持久性高于预设阈值的项，On-Off Sketch 会遍历**所有桶**。
    *   它会报告那些对应计数器 `B[h1(ei)][ei]` **大于给定阈值**的项 `ei` 的 ID。

总而言之，On-Off Sketch 通过其独特的“On-Off”状态字段机制，有效解决了数据流中持久性测量的误差问题，并通过区分和保护持久项来进一步提升了查找性能。其设计考虑了时间和内存效率，并能利用 SIMD 指令进行优化。

![alt text](image-16.png)
